《YYZ（抑郁症）Web应用产品需求与开发文档》

---

## 一、项目概述

### 1.1 项目目标
构建一款公益性质的Web应用，为抑郁症患者及其亲友提供科普、评估、陪伴与援助入口，具备良好的用户体验、心理专业性与技术可持续性。

### 1.2 项目定位
- 原型版本：快速呈现项目理念的MVP（最小可行产品）
- 展示功能：评估、AI陪聊、情绪记录、加入通道
- 用户角色：潜在用户（患者/关注者）、志愿者、专业人士、投资方

---

## 二、目标用户画像

| 用户类型 | 特征描述 |
|-----------|-----------|
| 抑郁症用户 | 孤独、低能量、渴望倾诉、可能回避就诊 |
| 普通公众 | 想了解心理知识、有家人朋友受困扰 |
| 医疗/社工专业 | 可协助平台设计和科普内容审核 |
| 志愿者 | 希望贡献力量，参与产品、内容、陪伴 |

---

## 三、核心功能模块

### 3.1 首页模块
- 项目介绍：理念、视频、创始人寄语
- 快捷导航：评估、陪聊、日志、加入我们
- 数据与引用：全球抑郁症数据引导关注

### 3.2 成症科普模块
- 科普文章（可图文/问答形式）
- 视频材料（合作心理学者/团队创作）
- 分类展示：青少年/职场人/老年人

### 3.3 心理评估模块
- PHQ-9（抑郁症）、GAD-7（焦虑症）量表简化版
- 每次评估结果图表呈现：雷达图/趋势曲线
- “我的记录”页面：按时间序列展示

### 3.4 AI数字陪聊模块
- GPT集成数字陪伴机器人
- 角色设定：
  - 倾听者（善于共情，少反馈）
  - 导师型（适度引导，鼓励思考）
  - 好朋友型（轻松陪聊）
- 输入界面支持文本、标签、表情反馈

### 3.5 情绪日志模块
- 每日记录：关键词 + 图标 + 简短描述
- 日历形式展示日志趋势
- 可生成简易图表：情绪高低周期识别

### 3.6 医疗数据与导出
- 自动生成评估记录PDF（可打印/分享）
- 用药记录、咨询次数手动录入
- 供线下医生参考的健康概览报告

### 3.7 加入我们模块
- 志愿者招募表单（前端/心理/传播）
- 合作单位申请入口（高校、NGO、企业）
- 项目动态、新闻与贡献鸣谢页

---

## 四、技术架构建议

### 前端
- 技术选型：React / Next.js + TailwindCSS
- 关键依赖：Recharts（数据图表）、Framer Motion（动画）、Shadcn UI组件库

### 后端
- Node.js（Express）或 Python（Flask/FastAPI）
- Firebase 作为匿名用户存储与日志同步方案
- MongoDB（如需构建医疗档案）

### AI陪聊引擎
- GPT API（OpenAI）或本地开源模型（初期推荐API）
- 会话数据隐私保护：默认不上传、临时性缓存

### 数据安全
- 前端本地缓存（IndexedDB/LocalStorage）
- 合规性提示：非临床平台，仅供参考，需用户确认协议
- GDPR/CN数据合规准备文档预案

---

## 五、开发计划（MVP）

| 时间 | 功能里程碑 |
|------|-------------|
| 第1-2周 | 项目官网首页，介绍、加入页、Logo与品牌方案 |
| 第3-4周 | 实现评估工具（PHQ-9）、AI陪聊界面初步原型 |
| 第5-6周 | 情绪日志功能、图表分析模块 |
| 第7-8周 | 用户注册/匿名系统、导出PDF机制 |
| 第9-10周 | 志愿者报名机制、社群启动工具包上线 |

---

## 六、附录

### 6.1 未来功能拓展
- 匿名聊天室（防骚扰机制）
- 地图展示附近资源点（医院、心理机构）
- 本地+国际双语言支持
- 联合研究模式接口（向高校开放脱敏数据）

### 6.2 风险提示
- 平台不具备临床治疗能力
- AI生成内容需专业审核并标注免责声明

---

## 七、结语

本应用是抑郁症数字关怀生态的起点，我们希望它不仅仅是工具，而是一个有温度、有力量、有成长空间的“心灵避风港”。让科技与善意同行，帮助每一个正在夜里独行的人。

> "你并不孤单，即使世界让你觉得如此。这里，总有人愿意听你说话。"
