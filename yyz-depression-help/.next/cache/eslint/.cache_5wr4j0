[{"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/assessment/page.tsx": "1", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/chat/page.tsx": "2", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/education/page.tsx": "3", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx": "4", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx": "5", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/mood/page.tsx": "6", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/page.tsx": "7", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/navigation.tsx": "8", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/ui/button.tsx": "9", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/ui/card.tsx": "10", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/ui/progress.tsx": "11", "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/lib/utils.ts": "12"}, {"size": 8896, "mtime": 1749089974553, "results": "13", "hashOfConfig": "14"}, {"size": 9822, "mtime": 1749090008717, "results": "15", "hashOfConfig": "14"}, {"size": 11626, "mtime": 1749090132613, "results": "16", "hashOfConfig": "14"}, {"size": 13654, "mtime": 1749090088462, "results": "17", "hashOfConfig": "14"}, {"size": 1092, "mtime": 1749089942729, "results": "18", "hashOfConfig": "14"}, {"size": 12782, "mtime": 1749090047053, "results": "19", "hashOfConfig": "14"}, {"size": 11472, "mtime": 1749089919209, "results": "20", "hashOfConfig": "14"}, {"size": 4896, "mtime": 1749089817084, "results": "21", "hashOfConfig": "14"}, {"size": 1975, "mtime": 1749089784686, "results": "22", "hashOfConfig": "14"}, {"size": 1876, "mtime": 1749089792891, "results": "23", "hashOfConfig": "14"}, {"size": 776, "mtime": 1749089798767, "results": "24", "hashOfConfig": "14"}, {"size": 5392, "mtime": 1749089770087, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1tjd13o", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/assessment/page.tsx", ["62", "63"], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/chat/page.tsx", [], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/education/page.tsx", ["64"], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx", ["65", "66", "67", "68"], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx", [], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/mood/page.tsx", ["69", "70", "71", "72", "73"], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/page.tsx", ["74", "75"], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/navigation.tsx", [], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/ui/button.tsx", ["76"], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/ui/card.tsx", [], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/ui/progress.tsx", [], [], "/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/lib/utils.ts", ["77"], [], {"ruleId": "78", "severity": 2, "message": "79", "line": 5, "column": 28, "nodeType": null, "messageId": "80", "endLine": 5, "endColumn": 38}, {"ruleId": "78", "severity": 2, "message": "81", "line": 14, "column": 10, "nodeType": null, "messageId": "80", "endLine": 14, "endColumn": 21}, {"ruleId": "78", "severity": 2, "message": "82", "line": 4, "column": 34, "nodeType": null, "messageId": "80", "endLine": 4, "endColumn": 39}, {"ruleId": "83", "severity": 2, "message": "84", "line": 144, "column": 27, "nodeType": "85", "messageId": "86", "suggestions": "87"}, {"ruleId": "83", "severity": 2, "message": "84", "line": 144, "column": 36, "nodeType": "85", "messageId": "86", "suggestions": "88"}, {"ruleId": "83", "severity": 2, "message": "84", "line": 322, "column": 19, "nodeType": "85", "messageId": "86", "suggestions": "89"}, {"ruleId": "83", "severity": 2, "message": "84", "line": 322, "column": 50, "nodeType": "85", "messageId": "86", "suggestions": "90"}, {"ruleId": "91", "severity": 2, "message": "92", "line": 27, "column": 41, "nodeType": "93", "messageId": "94", "endLine": 27, "endColumn": 44, "suggestions": "95"}, {"ruleId": "78", "severity": 2, "message": "96", "line": 62, "column": 18, "nodeType": null, "messageId": "80", "endLine": 62, "endColumn": 23}, {"ruleId": "91", "severity": 2, "message": "92", "line": 258, "column": 46, "nodeType": "93", "messageId": "94", "endLine": 258, "endColumn": 49, "suggestions": "97"}, {"ruleId": "91", "severity": 2, "message": "92", "line": 258, "column": 57, "nodeType": "93", "messageId": "94", "endLine": 258, "endColumn": 60, "suggestions": "98"}, {"ruleId": "91", "severity": 2, "message": "92", "line": 258, "column": 69, "nodeType": "93", "messageId": "94", "endLine": 258, "endColumn": 72, "suggestions": "99"}, {"ruleId": "83", "severity": 2, "message": "84", "line": 97, "column": 22, "nodeType": "85", "messageId": "86", "suggestions": "100"}, {"ruleId": "83", "severity": 2, "message": "84", "line": 97, "column": 31, "nodeType": "85", "messageId": "86", "suggestions": "101"}, {"ruleId": "78", "severity": 2, "message": "102", "line": 43, "column": 32, "nodeType": null, "messageId": "80", "endLine": 43, "endColumn": 39}, {"ruleId": "91", "severity": 2, "message": "92", "line": 151, "column": 29, "nodeType": "93", "messageId": "94", "endLine": 151, "endColumn": 32, "suggestions": "103"}, "@typescript-eslint/no-unused-vars", "'ArrowRight' is defined but never used.", "unusedVar", "'isCompleted' is assigned a value but never used.", "'Users' is defined but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["104", "105", "106", "107"], ["108", "109", "110", "111"], ["112", "113", "114", "115"], ["116", "117", "118", "119"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["120", "121"], "'index' is defined but never used.", ["122", "123"], ["124", "125"], ["126", "127"], ["128", "129", "130", "131"], ["132", "133", "134", "135"], "'as<PERSON><PERSON>d' is assigned a value but never used.", ["136", "137"], {"messageId": "138", "data": "139", "fix": "140", "desc": "141"}, {"messageId": "138", "data": "142", "fix": "143", "desc": "144"}, {"messageId": "138", "data": "145", "fix": "146", "desc": "147"}, {"messageId": "138", "data": "148", "fix": "149", "desc": "150"}, {"messageId": "138", "data": "151", "fix": "152", "desc": "141"}, {"messageId": "138", "data": "153", "fix": "154", "desc": "144"}, {"messageId": "138", "data": "155", "fix": "156", "desc": "147"}, {"messageId": "138", "data": "157", "fix": "158", "desc": "150"}, {"messageId": "138", "data": "159", "fix": "160", "desc": "141"}, {"messageId": "138", "data": "161", "fix": "162", "desc": "144"}, {"messageId": "138", "data": "163", "fix": "164", "desc": "147"}, {"messageId": "138", "data": "165", "fix": "166", "desc": "150"}, {"messageId": "138", "data": "167", "fix": "168", "desc": "141"}, {"messageId": "138", "data": "169", "fix": "170", "desc": "144"}, {"messageId": "138", "data": "171", "fix": "172", "desc": "147"}, {"messageId": "138", "data": "173", "fix": "174", "desc": "150"}, {"messageId": "175", "fix": "176", "desc": "177"}, {"messageId": "178", "fix": "179", "desc": "180"}, {"messageId": "175", "fix": "181", "desc": "177"}, {"messageId": "178", "fix": "182", "desc": "180"}, {"messageId": "175", "fix": "183", "desc": "177"}, {"messageId": "178", "fix": "184", "desc": "180"}, {"messageId": "175", "fix": "185", "desc": "177"}, {"messageId": "178", "fix": "186", "desc": "180"}, {"messageId": "138", "data": "187", "fix": "188", "desc": "141"}, {"messageId": "138", "data": "189", "fix": "190", "desc": "144"}, {"messageId": "138", "data": "191", "fix": "192", "desc": "147"}, {"messageId": "138", "data": "193", "fix": "194", "desc": "150"}, {"messageId": "138", "data": "195", "fix": "196", "desc": "141"}, {"messageId": "138", "data": "197", "fix": "198", "desc": "144"}, {"messageId": "138", "data": "199", "fix": "200", "desc": "147"}, {"messageId": "138", "data": "201", "fix": "202", "desc": "150"}, {"messageId": "175", "fix": "203", "desc": "177"}, {"messageId": "178", "fix": "204", "desc": "180"}, "replaceWithAlt", {"alt": "205"}, {"range": "206", "text": "207"}, "Replace with `&quot;`.", {"alt": "208"}, {"range": "209", "text": "210"}, "Replace with `&ldquo;`.", {"alt": "211"}, {"range": "212", "text": "213"}, "Replace with `&#34;`.", {"alt": "214"}, {"range": "215", "text": "216"}, "Replace with `&rdquo;`.", {"alt": "205"}, {"range": "217", "text": "218"}, {"alt": "208"}, {"range": "219", "text": "220"}, {"alt": "211"}, {"range": "221", "text": "222"}, {"alt": "214"}, {"range": "223", "text": "224"}, {"alt": "205"}, {"range": "225", "text": "226"}, {"alt": "208"}, {"range": "227", "text": "228"}, {"alt": "211"}, {"range": "229", "text": "230"}, {"alt": "214"}, {"range": "231", "text": "232"}, {"alt": "205"}, {"range": "233", "text": "234"}, {"alt": "208"}, {"range": "235", "text": "236"}, {"alt": "211"}, {"range": "237", "text": "238"}, {"alt": "214"}, {"range": "239", "text": "240"}, "suggestUnknown", {"range": "241", "text": "242"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "243", "text": "244"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "245", "text": "242"}, {"range": "246", "text": "244"}, {"range": "247", "text": "242"}, {"range": "248", "text": "244"}, {"range": "249", "text": "242"}, {"range": "250", "text": "244"}, {"alt": "205"}, {"range": "251", "text": "252"}, {"alt": "208"}, {"range": "253", "text": "254"}, {"alt": "211"}, {"range": "255", "text": "256"}, {"alt": "214"}, {"range": "257", "text": "258"}, {"alt": "205"}, {"range": "259", "text": "260"}, {"alt": "208"}, {"range": "261", "text": "262"}, {"alt": "211"}, {"range": "263", "text": "264"}, {"alt": "214"}, {"range": "265", "text": "266"}, {"range": "267", "text": "242"}, {"range": "268", "text": "244"}, "&quot;", [4798, 4966], "\n                  YYZ 是一个以&quot;抑郁症研究与帮助\"为核心目标的公益性项目。我们希望以数字技术为桥梁，\n                  让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声，\n                  为困顿者搭建桥梁。\n                ", "&ldquo;", [4798, 4966], "\n                  YYZ 是一个以&ldquo;抑郁症研究与帮助\"为核心目标的公益性项目。我们希望以数字技术为桥梁，\n                  让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声，\n                  为困顿者搭建桥梁。\n                ", "&#34;", [4798, 4966], "\n                  YYZ 是一个以&#34;抑郁症研究与帮助\"为核心目标的公益性项目。我们希望以数字技术为桥梁，\n                  让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声，\n                  为困顿者搭建桥梁。\n                ", "&rdquo;", [4798, 4966], "\n                  YYZ 是一个以&rdquo;抑郁症研究与帮助\"为核心目标的公益性项目。我们希望以数字技术为桥梁，\n                  让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声，\n                  为困顿者搭建桥梁。\n                ", [4798, 4966], "\n                  YYZ 是一个以\"抑郁症研究与帮助&quot;为核心目标的公益性项目。我们希望以数字技术为桥梁，\n                  让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声，\n                  为困顿者搭建桥梁。\n                ", [4798, 4966], "\n                  YYZ 是一个以\"抑郁症研究与帮助&ldquo;为核心目标的公益性项目。我们希望以数字技术为桥梁，\n                  让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声，\n                  为困顿者搭建桥梁。\n                ", [4798, 4966], "\n                  YYZ 是一个以\"抑郁症研究与帮助&#34;为核心目标的公益性项目。我们希望以数字技术为桥梁，\n                  让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声，\n                  为困顿者搭建桥梁。\n                ", [4798, 4966], "\n                  YYZ 是一个以\"抑郁症研究与帮助&rdquo;为核心目标的公益性项目。我们希望以数字技术为桥梁，\n                  让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声，\n                  为困顿者搭建桥梁。\n                ", [12407, 12475], "\n                  &quot;如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。\"\n                ", [12407, 12475], "\n                  &ldquo;如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。\"\n                ", [12407, 12475], "\n                  &#34;如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。\"\n                ", [12407, 12475], "\n                  &rdquo;如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。\"\n                ", [12407, 12475], "\n                  \"如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。&quot;\n                ", [12407, 12475], "\n                  \"如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。&ldquo;\n                ", [12407, 12475], "\n                  \"如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。&#34;\n                ", [12407, 12475], "\n                  \"如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。&rdquo;\n                ", [955, 958], "unknown", [955, 958], "never", [9896, 9899], [9896, 9899], [9907, 9910], [9907, 9910], [9919, 9922], [9919, 9922], [2955, 3035], "\n              YYZ是一个以&quot;抑郁症研究与帮助\"为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。\n            ", [2955, 3035], "\n              YYZ是一个以&ldquo;抑郁症研究与帮助\"为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。\n            ", [2955, 3035], "\n              YYZ是一个以&#34;抑郁症研究与帮助\"为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。\n            ", [2955, 3035], "\n              YYZ是一个以&rdquo;抑郁症研究与帮助\"为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。\n            ", [2955, 3035], "\n              YYZ是一个以\"抑郁症研究与帮助&quot;为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。\n            ", [2955, 3035], "\n              YYZ是一个以\"抑郁症研究与帮助&ldquo;为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。\n            ", [2955, 3035], "\n              YYZ是一个以\"抑郁症研究与帮助&#34;为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。\n            ", [2955, 3035], "\n              YYZ是一个以\"抑郁症研究与帮助&rdquo;为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。\n            ", [3724, 3727], [3724, 3727]]