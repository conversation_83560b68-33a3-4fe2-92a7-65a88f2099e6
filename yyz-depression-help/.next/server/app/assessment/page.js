(()=>{var e={};e.id=197,e.ids=[197],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35245:(e,t,r)=>{Promise.resolve().then(r.bind(r,96937))},40631:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),l=r(88170),n=r.n(l),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["assessment",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96937)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/assessment/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/assessment/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/assessment/page",pathname:"/assessment",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=r(60687),a=r(43210),l=r(4780);let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},82509:(e,t,r)=>{Promise.resolve().then(r.bind(r,87909))},87909:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var s=r(60687),a=r(43210),l=r(21121),n=r(5336),i=r(78200),o=r(28559),d=r(29523),c=r(44493);function u(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var m=Symbol("radix.slottable");function p(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===m}var x=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...s}=e;if(a.isValidElement(r)){var l;let e,n,i=(l=r,(n=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(n=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),o=function(e,t){let r={...t};for(let s in t){let a=e[s],l=t[s];/^on[A-Z]/.test(s)?a&&l?r[s]=(...e)=>{let t=l(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...l}:"className"===s&&(r[s]=[a,l].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==a.Fragment&&(o.ref=t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=u(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():u(e[t],null)}}}}(t,i):i),a.cloneElement(r,o)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:l,...n}=e,i=a.Children.toArray(l),o=i.find(p);if(o){let e=o.props.children,l=i.map(t=>t!==o?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...n,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,l):null})}return(0,s.jsx)(t,{...n,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),l=a.forwardRef((e,a)=>{let{asChild:l,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(l?r:t,{...n,ref:a})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),f="Progress",[h,v]=function(e,t=[]){let r=[],l=()=>{let t=r.map(e=>a.createContext(e));return function(r){let s=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:s}}),[r,s])}};return l.scopeName=e,[function(t,l){let n=a.createContext(l),i=r.length;r=[...r,l];let o=t=>{let{scope:r,children:l,...o}=t,d=r?.[e]?.[i]||n,c=a.useMemo(()=>o,Object.values(o));return(0,s.jsx)(d.Provider,{value:c,children:l})};return o.displayName=t+"Provider",[o,function(r,s){let o=s?.[e]?.[i]||n,d=a.useContext(o);if(d)return d;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=r.reduce((t,{useScope:r,scopeName:s})=>{let a=r(e)[`__scope${s}`];return{...t,...a}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return r.scopeName=t.scopeName,r}(l,...t)]}(f),[y,j]=h(f),b=a.forwardRef((e,t)=>{var r,a;let{__scopeProgress:l,value:n=null,max:i,getValueLabel:o=w,...d}=e;(i||0===i)&&!C(i)&&console.error((r=`${i}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let c=C(i)?i:100;null===n||R(n,c)||console.error((a=`${n}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=R(n,c)?n:null,m=$(u)?o(u,c):void 0;return(0,s.jsx)(y,{scope:l,value:u,max:c,children:(0,s.jsx)(x.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":$(u)?u:void 0,"aria-valuetext":m,role:"progressbar","data-state":P(u,c),"data-value":u??void 0,"data-max":c,...d,ref:t})})});b.displayName=f;var g="ProgressIndicator",N=a.forwardRef((e,t)=>{let{__scopeProgress:r,...a}=e,l=j(g,r);return(0,s.jsx)(x.div,{"data-state":P(l.value,l.max),"data-value":l.value??void 0,"data-max":l.max,...a,ref:t})});function w(e,t){return`${Math.round(e/t*100)}%`}function P(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function $(e){return"number"==typeof e}function C(e){return $(e)&&!isNaN(e)&&e>0}function R(e,t){return $(e)&&!isNaN(e)&&e<=t&&e>=0}N.displayName=g;var _=r(4780);let Z=a.forwardRef(({className:e,value:t,...r},a)=>(0,s.jsx)(b,{ref:a,className:(0,_.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,s.jsx)(N,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));function k(){let[e,t]=(0,a.useState)(0),[r,u]=(0,a.useState)(Array(_.$Z.length).fill(-1)),[,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1),f=(e+1)/_.$Z.length*100,h=r.reduce((e,t)=>e+(t>-1?t:0),0),v=(0,_.aX)(h),y=s=>{let a=[...r];a[e]=s,u(a),e<_.$Z.length-1?setTimeout(()=>{t(e+1)},300):(m(!0),setTimeout(()=>{x(!0)},500))};return p?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-12",children:(0,s.jsx)("div",{className:"mx-auto max-w-4xl px-6",children:(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,s.jsx)(c.Zp,{className:"mb-8",children:(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"w-8 h-8 text-white"})}),(0,s.jsx)(c.ZB,{className:"text-2xl",children:"评估完成"}),(0,s.jsx)(c.BT,{children:"感谢您完成PHQ-9抑郁症评估量表"})]})}),(0,s.jsxs)(c.Zp,{className:`mb-8 border-2 ${v.borderColor}`,children:[(0,s.jsxs)(c.aR,{className:`${v.bgColor} rounded-t-lg`,children:[(0,s.jsxs)(c.ZB,{className:`text-xl ${v.color}`,children:["评估结果：",v.title]}),(0,s.jsxs)("div",{className:"text-3xl font-bold text-gray-900",children:["总分：",h,"/27"]})]}),(0,s.jsxs)(c.Wu,{className:"pt-6",children:[(0,s.jsx)("p",{className:"text-gray-700 mb-6",children:v.description}),(0,s.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:[(0,s.jsx)("h4",{className:"font-semibold text-yellow-800 mb-2",children:"重要提醒"}),(0,s.jsx)("p",{className:"text-yellow-700 text-sm",children:"本评估仅供参考，不能替代专业医疗诊断。如果您感到困扰或有自伤想法，请立即寻求专业帮助。"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(d.$,{onClick:()=>{t(0),u(Array(_.$Z.length).fill(-1)),m(!1),x(!1)},variant:"outline",className:"w-full",children:"重新评估"}),(0,s.jsx)(d.$,{variant:"warm",className:"w-full",children:"寻求帮助"})]})]})]}),(0,s.jsxs)(c.Zp,{children:[(0,s.jsx)(c.aR,{children:(0,s.jsx)(c.ZB,{children:"您的答案回顾"})}),(0,s.jsx)(c.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:_.$Z.map((e,t)=>(0,s.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600 flex-1",children:e.question}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900 ml-4",children:_.C1[r[t]]?.label||"未回答"})]},e.id))})})]})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-12",children:(0,s.jsx)("div",{className:"mx-auto max-w-4xl px-6",children:(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,s.jsx)(c.Zp,{className:"mb-8",children:(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,s.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,s.jsx)(c.ZB,{className:"text-2xl",children:"PHQ-9 抑郁症评估"}),(0,s.jsx)(c.BT,{children:"请根据过去两周的感受，选择最符合您情况的选项"})]})}),(0,s.jsx)(c.Zp,{className:"mb-8",children:(0,s.jsxs)(c.Wu,{className:"pt-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:["问题 ",e+1," / ",_.$Z.length]}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[Math.round(f),"% 完成"]})]}),(0,s.jsx)(Z,{value:f,className:"h-2"})]})}),(0,s.jsx)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5},children:(0,s.jsxs)(c.Zp,{className:"mb-8",children:[(0,s.jsxs)(c.aR,{children:[(0,s.jsx)(c.ZB,{className:"text-xl",children:_.$Z[e].question}),(0,s.jsx)(c.BT,{children:_.$Z[e].description})]}),(0,s.jsx)(c.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:_.C1.map(t=>(0,s.jsx)(l.P.button,{onClick:()=>y(t.value),className:`w-full p-4 text-left rounded-lg border-2 transition-all hover:border-blue-300 hover:bg-blue-50 ${r[e]===t.value?"border-blue-500 bg-blue-50":"border-gray-200 bg-white"}`,whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:t.label}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:t.days})]}),(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.value})]})},t.value))})})]})},e),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)(d.$,{onClick:()=>{e>0&&t(e-1)},variant:"outline",disabled:0===e,className:"flex items-center space-x-2",children:[(0,s.jsx)(o.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"上一题"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500 flex items-center",children:"点击选项自动进入下一题"})]})]})})})}Z.displayName=b.displayName},96937:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/assessment/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/assessment/page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,242,820,725],()=>r(40631));module.exports=s})();