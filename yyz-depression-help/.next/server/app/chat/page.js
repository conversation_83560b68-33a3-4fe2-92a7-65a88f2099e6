(()=>{var e={};e.id=457,e.ids=[457],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8973:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/chat/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/chat/page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16801:(e,t,s)=>{Promise.resolve().then(s.bind(s,8973))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27900:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35543:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8973)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/chat/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/chat/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/chat/page",pathname:"/chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>l});var a=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let l=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));o.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},53249:(e,t,s)=>{Promise.resolve().then(s.bind(s,72228))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72228:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(60687),r=s(43210),i=s(21121),n=s(33872),l=s(62688);let d=(0,l.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),o=(0,l.A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);var c=s(27900),p=s(67760);let m=(0,l.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);var x=s(29523),u=s(44493);let h=["我听到了你的话，感谢你愿意和我分享。你现在的感受是完全可以理解的。","每个人都有低落的时候，这并不意味着你有什么问题。你愿意告诉我更多吗？","你的感受很重要，我想更好地理解你现在的状况。","听起来你正在经历一些困难。记住，寻求帮助是勇敢的表现。","我能感受到你的痛苦。虽然我是AI，但我希望能给你一些温暖和支持。","你不是一个人在面对这些。有很多人关心你，包括专业的帮助者。","每一天都是新的开始。即使现在很难，但请相信事情会慢慢好起来的。","你已经很勇敢了，能够表达自己的感受需要很大的勇气。"],f=["我感到很沮丧","今天过得不太好","我觉得很孤独","我想找人聊聊","我需要一些鼓励","我感到焦虑不安"];function y(){let[e,t]=(0,r.useState)([{id:"1",content:"你好！我是你的AI陪伴者小光。我在这里倾听你的心声，陪伴你度过困难时光。请随时告诉我你的感受，我会尽我所能给你支持和理解。",sender:"ai",timestamp:new Date}]),[s,l]=(0,r.useState)(""),[y,v]=(0,r.useState)(!1),g=(0,r.useRef)(null),j=async e=>{if(!e.trim())return;let s={id:Date.now().toString(),content:e.trim(),sender:"user",timestamp:new Date};t(e=>[...e,s]),l(""),v(!0),setTimeout(()=>{let e={id:(Date.now()+1).toString(),content:h[Math.floor(Math.random()*h.length)],sender:"ai",timestamp:new Date};t(t=>[...t,e]),v(!1)},1e3+2e3*Math.random())},b=e=>{j(e)};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50",children:(0,a.jsxs)("div",{className:"mx-auto max-w-4xl px-6 py-8",children:[(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:(0,a.jsx)(u.Zp,{className:"mb-6",children:(0,a.jsxs)(u.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(n.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)(u.ZB,{className:"text-2xl",children:"AI陪聊"}),(0,a.jsx)(u.BT,{children:"24小时温暖陪伴，倾听你的心声"})]})})}),(0,a.jsxs)(u.Zp,{className:"h-[600px] flex flex-col",children:[(0,a.jsxs)(u.Wu,{className:"flex-1 overflow-y-auto p-6 space-y-4",children:[e.map(e=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:`flex ${"user"===e.sender?"justify-end":"justify-start"}`,children:(0,a.jsxs)("div",{className:`flex items-start space-x-3 max-w-[80%] ${"user"===e.sender?"flex-row-reverse space-x-reverse":""}`,children:[(0,a.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"user"===e.sender?"bg-blue-500":"bg-gradient-to-br from-purple-500 to-pink-600"}`,children:"user"===e.sender?(0,a.jsx)(d,{className:"w-4 h-4 text-white"}):(0,a.jsx)(o,{className:"w-4 h-4 text-white"})}),(0,a.jsxs)("div",{className:`rounded-lg p-3 ${"user"===e.sender?"bg-blue-500 text-white":"bg-gray-100 text-gray-900"}`,children:[(0,a.jsx)("p",{className:"text-sm",children:e.content}),(0,a.jsx)("p",{className:`text-xs mt-1 ${"user"===e.sender?"text-blue-100":"text-gray-500"}`,children:e.timestamp.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"})})]})]})},e.id)),y&&(0,a.jsx)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex justify-start",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center",children:(0,a.jsx)(o,{className:"w-4 h-4 text-white"})}),(0,a.jsx)("div",{className:"bg-gray-100 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]})}),(0,a.jsx)("div",{ref:g})]}),1===e.length&&(0,a.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"快速开始对话："}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:f.map((e,t)=>(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>b(e),className:"text-xs",children:e},t))})]}),(0,a.jsxs)("div",{className:"p-6 border-t border-gray-200",children:[(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),j(s)},className:"flex space-x-3",children:[(0,a.jsx)("input",{type:"text",value:s,onChange:e=>l(e.target.value),placeholder:"输入你想说的话...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",disabled:y}),(0,a.jsx)(x.$,{type:"submit",disabled:!s.trim()||y,className:"bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700",children:(0,a.jsx)(c.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2 text-center",children:[(0,a.jsx)(p.A,{className:"w-3 h-3 inline mr-1"}),"AI陪聊仅供情感支持，不能替代专业心理咨询"]})]})]}),(0,a.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-6",children:(0,a.jsx)(u.Zp,{className:"bg-yellow-50 border-yellow-200",children:(0,a.jsx)(u.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(m,{className:"w-5 h-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-yellow-800 mb-1",children:"温馨提示"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700",children:"我是AI陪伴者，可以倾听和陪伴，但无法提供专业的心理治疗。如果您需要专业帮助，请联系心理咨询师或医生。"})]})]})})})})]})})}},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,527,820,725],()=>s(35543));module.exports=a})();