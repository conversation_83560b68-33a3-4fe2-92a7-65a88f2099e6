(()=>{var e={};e.id=597,e.ids=[597],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7587:(e,s,r)=>{Promise.resolve().then(r.bind(r,63483))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15807:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(62688).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21659:(e,s,r)=>{Promise.resolve().then(r.bind(r,25001))},25001:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var l=r(60687),t=r(21121),a=r(28559),i=r(10022),n=r(28947),c=r(15807),d=r(67760),o=r(41312),x=r(85814),h=r.n(x),m=r(29523),p=r(44493);function j(){return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50",children:(0,l.jsxs)("div",{className:"mx-auto max-w-4xl px-6 py-8",children:[(0,l.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsx)(m.$,{variant:"ghost",asChild:!0,className:"mb-4",children:(0,l.jsxs)(h(),{href:"/about",className:"flex items-center space-x-2",children:[(0,l.jsx)(a.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"返回关于我们"})]})})}),(0,l.jsx)(p.Zp,{className:"mb-8",children:(0,l.jsxs)(p.aR,{className:"text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,l.jsx)(p.ZB,{className:"text-3xl",children:"项目初衷"}),(0,l.jsx)(p.BT,{className:"text-lg",children:"YYZ（抑郁症）Web应用产品需求与开发文档"})]})})]}),(0,l.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"space-y-8",children:[(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(n.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"项目背景"})]})}),(0,l.jsxs)(p.Wu,{className:"prose prose-gray max-w-none",children:[(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"抑郁症是一种常见的心理疾病，全球有超过3.5亿人受其影响。在中国，抑郁症的患病率约为4.2%， 但由于社会认知不足、就医门槛高等问题，大多数患者无法得到及时有效的帮助。"}),(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"我们发现，传统的心理健康服务存在以下问题："}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2",children:[(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"可及性差"}),"：专业心理咨询师资源稀缺，预约困难"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"成本高昂"}),"：心理咨询费用对普通人群负担较重"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"隐私担忧"}),"：面对面咨询让很多人感到不自在"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"缺乏连续性"}),"：缺少日常的情绪监测和支持"]})]})]})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(c.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"解决方案"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"技术创新"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• AI驱动的智能陪伴系统"}),(0,l.jsx)("li",{children:"• 基于PHQ-9的专业评估工具"}),(0,l.jsx)("li",{children:"• 数据可视化的情绪追踪"}),(0,l.jsx)("li",{children:"• 个性化的内容推荐"})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"服务理念"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 24/7全天候可用"}),(0,l.jsx)("li",{children:"• 完全免费的公益服务"}),(0,l.jsx)("li",{children:"• 匿名使用保护隐私"}),(0,l.jsx)("li",{children:"• 专业志愿者支持"})]})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(d.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"核心功能模块"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"1. 心理评估系统"}),(0,l.jsx)("p",{className:"text-blue-700 text-sm",children:"基于PHQ-9量表的标准化抑郁症评估，提供专业的结果解读和建议。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-purple-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-purple-900 mb-2",children:"2. AI陪伴聊天"}),(0,l.jsx)("p",{className:"text-purple-700 text-sm",children:"智能对话系统提供情感支持，24小时倾听用户心声。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"3. 情绪日志"}),(0,l.jsx)("p",{className:"text-green-700 text-sm",children:"记录日常情绪变化，通过数据分析帮助用户了解情绪模式。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"4. 科普教育"}),(0,l.jsx)("p",{className:"text-orange-700 text-sm",children:"提供权威的抑郁症相关知识，帮助用户正确认识心理健康。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-teal-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-teal-900 mb-2",children:"5. 医疗数据管理"}),(0,l.jsx)("p",{className:"text-teal-700 text-sm",children:"整合健康记录，生成专业报告，便于与医生沟通。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-pink-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-pink-900 mb-2",children:"6. 社区支持"}),(0,l.jsx)("p",{className:"text-pink-700 text-sm",children:"志愿者网络和用户社区，提供人文关怀和互助支持。"})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(o.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"目标用户群体"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 mx-auto mb-3 bg-blue-100 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-blue-600 font-bold",children:"1"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"轻度抑郁症患者"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"需要早期干预和日常支持的用户"})]}),(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 mx-auto mb-3 bg-purple-100 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-purple-600 font-bold",children:"2"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"高风险人群"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"学生、职场人士等压力较大的群体"})]}),(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 mx-auto mb-3 bg-green-100 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-green-600 font-bold",children:"3"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"关注者"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"患者家属、朋友及心理健康关注者"})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsx)(p.ZB,{children:"技术架构"})}),(0,l.jsx)(p.Wu,{children:(0,l.jsx)("div",{className:"bg-gray-50 p-6 rounded-lg",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"前端技术栈"}),(0,l.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,l.jsx)("li",{children:"• Next.js 15 + React 18"}),(0,l.jsx)("li",{children:"• TypeScript"}),(0,l.jsx)("li",{children:"• TailwindCSS + Shadcn UI"}),(0,l.jsx)("li",{children:"• Framer Motion"}),(0,l.jsx)("li",{children:"• Recharts"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"后端规划"}),(0,l.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,l.jsx)("li",{children:"• Node.js + Express"}),(0,l.jsx)("li",{children:"• PostgreSQL 数据库"}),(0,l.jsx)("li",{children:"• OpenAI GPT API"}),(0,l.jsx)("li",{children:"• Redis 缓存"}),(0,l.jsx)("li",{children:"• Docker 容器化"})]})]})]})})})]}),(0,l.jsx)(p.Zp,{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,l.jsx)(p.Wu,{className:"pt-6",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"text-xl font-bold mb-4",children:"加入我们的使命"}),(0,l.jsx)("p",{className:"mb-6 text-blue-100",children:"如果您认同我们的理念，愿意为心理健康事业贡献力量，欢迎加入我们！"}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,l.jsx)(m.$,{size:"lg",variant:"secondary",asChild:!0,children:(0,l.jsx)(h(),{href:"/join",children:"成为志愿者"})}),(0,l.jsx)(m.$,{size:"lg",variant:"outline",className:"text-white border-white hover:bg-white hover:text-blue-600",asChild:!0,children:(0,l.jsx)(h(),{href:"/about",children:"返回关于我们"})})]})]})})})]})]})})}},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28947:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n});var l=r(60687),t=r(43210),a=r(4780);let i=t.forwardRef(({className:e,...s},r)=>(0,l.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let n=t.forwardRef(({className:e,...s},r)=>(0,l.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let c=t.forwardRef(({className:e,...s},r)=>(0,l.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let d=t.forwardRef(({className:e,...s},r)=>(0,l.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let o=t.forwardRef(({className:e,...s},r)=>(0,l.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",t.forwardRef(({className:e,...s},r)=>(0,l.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63483:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});let l=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/docs/project-vision/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/docs/project-vision/page.tsx","default")},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});var l=r(31658);let t=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,l.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},88431:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var l=r(65239),t=r(48088),a=r(88170),i=r.n(a),n=r(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(s,c);let d={children:["",{children:["docs",{children:["project-vision",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,63483)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/docs/project-vision/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/docs/project-vision/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},h=new l.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/docs/project-vision/page",pathname:"/docs/project-vision",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),l=s.X(0,[447,242,820,725],()=>r(88431));module.exports=l})();