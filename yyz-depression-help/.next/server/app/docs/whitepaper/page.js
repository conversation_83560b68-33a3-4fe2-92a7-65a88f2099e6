(()=>{var e={};e.id=272,e.ids=[272],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7462:(e,s,r)=>{Promise.resolve().then(r.bind(r,99492))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},16278:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});let l=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/docs/whitepaper/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/docs/whitepaper/page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,s,r)=>{"use strict";r.d(s,{BT:()=>c,Wu:()=>x,ZB:()=>n,Zp:()=>i,aR:()=>d});var l=r(60687),a=r(43210),t=r(4780);let i=a.forwardRef(({className:e,...s},r)=>(0,l.jsx)("div",{ref:r,className:(0,t.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let d=a.forwardRef(({className:e,...s},r)=>(0,l.jsx)("div",{ref:r,className:(0,t.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let n=a.forwardRef(({className:e,...s},r)=>(0,l.jsx)("h3",{ref:r,className:(0,t.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));n.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},r)=>(0,l.jsx)("p",{ref:r,className:(0,t.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let x=a.forwardRef(({className:e,...s},r)=>(0,l.jsx)("div",{ref:r,className:(0,t.cn)("p-6 pt-0",e),...s}));x.displayName="CardContent",a.forwardRef(({className:e,...s},r)=>(0,l.jsx)("div",{ref:r,className:(0,t.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},47835:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>c});var l=r(65239),a=r(48088),t=r(88170),i=r.n(t),d=r(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(s,n);let c={children:["",{children:["docs",{children:["whitepaper",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,16278)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/docs/whitepaper/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/docs/whitepaper/page.tsx"],o={require:r,loadChunk:()=>Promise.resolve()},h=new l.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/docs/whitepaper/page",pathname:"/docs/whitepaper",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53411:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67294:(e,s,r)=>{Promise.resolve().then(r.bind(r,16278))},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var l=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,l.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},99492:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var l=r(60687),a=r(21121),t=r(28559),i=r(82080),d=r(53411),n=r(41312),c=r(99891),x=r(11437),o=r(85814),h=r.n(o),m=r(29523),p=r(44493);function j(){return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50",children:(0,l.jsxs)("div",{className:"mx-auto max-w-4xl px-6 py-8",children:[(0,l.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsx)(m.$,{variant:"ghost",asChild:!0,className:"mb-4",children:(0,l.jsxs)(h(),{href:"/about",className:"flex items-center space-x-2",children:[(0,l.jsx)(t.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"返回关于我们"})]})})}),(0,l.jsx)(p.Zp,{className:"mb-8",children:(0,l.jsxs)(p.aR,{className:"text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,l.jsx)(p.ZB,{className:"text-3xl",children:"项目白皮书"}),(0,l.jsx)(p.BT,{className:"text-lg",children:"YYZ抑郁症研究与帮助平台：数字化心理健康服务的创新实践"})]})})]}),(0,l.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"space-y-8",children:[(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsx)(p.ZB,{children:"执行摘要"})}),(0,l.jsxs)(p.Wu,{className:"prose prose-gray max-w-none",children:[(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"YYZ项目是一个创新的数字化心理健康服务平台，专注于为抑郁症患者及高风险人群提供 全方位的支持服务。通过整合人工智能、专业心理学知识和社区力量，我们致力于降低 心理健康服务的门槛，提高服务的可及性和有效性。"}),(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"本白皮书详细阐述了项目的社会价值、技术创新、商业模式和发展规划，为投资者、 合作伙伴和社会各界了解项目提供全面的参考。"})]})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(d.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"市场分析"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"市场规模"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg",children:[(0,l.jsx)("span",{className:"text-sm font-medium",children:"全球抑郁症患者"}),(0,l.jsx)("span",{className:"text-blue-600 font-bold",children:"3.5亿+"})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center p-3 bg-purple-50 rounded-lg",children:[(0,l.jsx)("span",{className:"text-sm font-medium",children:"中国患病率"}),(0,l.jsx)("span",{className:"text-purple-600 font-bold",children:"4.2%"})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center p-3 bg-green-50 rounded-lg",children:[(0,l.jsx)("span",{className:"text-sm font-medium",children:"就医率"}),(0,l.jsx)("span",{className:"text-green-600 font-bold",children:"仅10%"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"市场痛点"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 专业心理咨询师资源严重不足"}),(0,l.jsx)("li",{children:"• 传统服务成本高昂，普及率低"}),(0,l.jsx)("li",{children:"• 社会认知不足，存在偏见和歧视"}),(0,l.jsx)("li",{children:"• 缺乏有效的早期筛查和干预机制"}),(0,l.jsx)("li",{children:"• 数字化服务质量参差不齐"})]})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsx)(p.ZB,{children:"创新亮点"})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"p-4 border border-blue-200 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"\uD83E\uDD16 AI技术创新"}),(0,l.jsx)("p",{className:"text-blue-700 text-sm",children:"基于大语言模型的智能陪伴系统，提供个性化的情感支持和专业建议。"})]}),(0,l.jsxs)("div",{className:"p-4 border border-purple-200 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-purple-900 mb-2",children:"\uD83D\uDCCA 数据驱动"}),(0,l.jsx)("p",{className:"text-purple-700 text-sm",children:"通过情绪追踪和行为分析，为用户提供科学的心理健康洞察。"})]}),(0,l.jsxs)("div",{className:"p-4 border border-green-200 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"\uD83C\uDFE5 医疗整合"}),(0,l.jsx)("p",{className:"text-green-700 text-sm",children:"与传统医疗体系无缝对接，为线下就医提供数据支持。"})]}),(0,l.jsxs)("div",{className:"p-4 border border-orange-200 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"\uD83C\uDF10 社区生态"}),(0,l.jsx)("p",{className:"text-orange-700 text-sm",children:"构建专业志愿者网络，形成可持续的社区支持体系。"})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(n.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"社会价值"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white text-2xl font-bold",children:"1M+"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"预期服务用户"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"3年内服务超过100万用户"})]}),(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white text-2xl font-bold",children:"24/7"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"全天候服务"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"打破时间和地域限制"})]}),(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white text-2xl font-bold",children:"0\xa5"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"完全免费"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"降低心理健康服务门槛"})]})]}),(0,l.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"预期社会效益"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"提高早期识别率"}),"：通过便捷的评估工具，帮助更多人及早发现心理健康问题"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"减少社会成本"}),"：预防性干预降低重度抑郁症的治疗成本"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"促进社会认知"}),"：通过科普教育消除对心理疾病的偏见"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"培养专业人才"}),"：为心理健康领域培养更多志愿者和专业人士"]})]})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(c.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"隐私与安全"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"数据保护措施"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 端到端加密传输"}),(0,l.jsx)("li",{children:"• 本地数据存储优先"}),(0,l.jsx)("li",{children:"• 匿名化数据处理"}),(0,l.jsx)("li",{children:"• 定期安全审计"}),(0,l.jsx)("li",{children:"• 符合GDPR等国际标准"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"用户权利保障"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 完全匿名使用权"}),(0,l.jsx)("li",{children:"• 数据删除权"}),(0,l.jsx)("li",{children:"• 服务退出权"}),(0,l.jsx)("li",{children:"• 透明的隐私政策"}),(0,l.jsx)("li",{children:"• 用户数据控制权"})]})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(x.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"发展规划"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"短期目标（6-12个月）"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 完成MVP开发和测试"}),(0,l.jsx)("li",{children:"• 招募100名专业志愿者"}),(0,l.jsx)("li",{children:"• 服务1万名用户"}),(0,l.jsx)("li",{children:"• 建立合作伙伴网络"})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"中期目标（1-3年）"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 用户规模达到100万"}),(0,l.jsx)("li",{children:"• 推出移动应用"}),(0,l.jsx)("li",{children:"• 与医疗机构深度合作"}),(0,l.jsx)("li",{children:"• 开展学术研究合作"})]})]})]}),(0,l.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"长期愿景（3-5年）"}),(0,l.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:"成为中国领先的数字化心理健康服务平台，建立覆盖全国的志愿者网络， 与政府、医疗机构、高校等建立深度合作关系，推动心理健康服务的 标准化和普及化，为构建健康中国贡献力量。"})]})]})})]}),(0,l.jsx)(p.Zp,{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,l.jsx)(p.Wu,{className:"pt-6",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"text-xl font-bold mb-4",children:"携手共建心理健康生态"}),(0,l.jsx)("p",{className:"mb-6 text-blue-100",children:"我们诚邀各界人士加入这一有意义的事业，共同为心理健康服务的创新发展贡献力量。"}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,l.jsx)(m.$,{size:"lg",variant:"secondary",asChild:!0,children:(0,l.jsx)(h(),{href:"/join",children:"加入我们"})}),(0,l.jsx)(m.$,{size:"lg",variant:"outline",className:"text-white border-white hover:bg-white hover:text-blue-600",asChild:!0,children:(0,l.jsx)(h(),{href:"/docs/project-vision",children:"查看项目初衷"})})]})]})})})]})]})})}},99891:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),l=s.X(0,[447,242,820,725],()=>r(47835));module.exports=l})();