(()=>{var e={};e.id=651,e.ids=[651],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7327:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["education",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,22185)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/education/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/education/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/education/page",pathname:"/education",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22185:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/education/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/education/page.tsx","default")},27735:(e,t,s)=>{Promise.resolve().then(s.bind(s,72873))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>l});var r=s(60687),a=s(43210),i=s(4780);let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));o.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72873:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(60687),a=s(21121),i=s(82080),n=s(70334);let l=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var d=s(5336),o=s(67760),c=s(78200),x=s(85814),m=s.n(x),p=s(29523),h=s(44493);let u=[{id:1,title:"什么是抑郁症？",description:"了解抑郁症的基本概念、症状表现和常见误区",category:"基础知识",readTime:"5分钟",color:"from-blue-500 to-cyan-500",content:`
      抑郁症是一种常见的心理疾病，影响着全球超过3.5亿人。它不仅仅是"心情不好"，
      而是一种持续的、严重影响日常生活的情绪障碍。

      主要症状包括：
      • 持续的悲伤或空虚感
      • 对平时喜欢的活动失去兴趣
      • 疲劳和精力不足
      • 睡眠问题
      • 食欲变化
      • 注意力难以集中
      • 自我价值感低
      • 有时会有自伤想法

      重要的是要知道，抑郁症是可以治疗的，寻求帮助是勇敢的表现。
    `},{id:2,title:"如何识别抑郁症的早期信号",description:"学会识别自己和他人的抑郁症早期症状",category:"识别诊断",readTime:"7分钟",color:"from-green-500 to-emerald-500",content:`
      早期识别抑郁症对于及时干预非常重要。以下是一些需要关注的信号：

      情绪变化：
      • 持续两周以上的低落情绪
      • 对未来感到绝望
      • 易怒或焦虑
      • 情绪波动大

      行为变化：
      • 社交退缩，避免与人接触
      • 工作或学习效率下降
      • 个人卫生习惯改变
      • 睡眠模式改变

      身体症状：
      • 不明原因的疲劳
      • 头痛或身体疼痛
      • 消化问题
      • 食欲变化

      如果这些症状持续存在并影响日常生活，建议寻求专业帮助。
    `},{id:3,title:"抑郁症的治疗方法",description:"了解现代医学对抑郁症的治疗方案",category:"治疗康复",readTime:"8分钟",color:"from-purple-500 to-pink-500",content:`
      抑郁症有多种有效的治疗方法，通常需要个性化的治疗方案：

      心理治疗：
      • 认知行为疗法(CBT)
      • 人际关系疗法
      • 正念疗法
      • 家庭治疗

      药物治疗：
      • 抗抑郁药物
      • 需要专业医生指导
      • 通常需要4-6周见效
      • 不要自行停药

      生活方式调整：
      • 规律运动
      • 健康饮食
      • 充足睡眠
      • 社交支持
      • 压力管理

      综合治疗效果最佳，重要的是要有耐心，康复是一个过程。
    `},{id:4,title:"如何帮助身边的抑郁症患者",description:"学习如何为抑郁症患者提供支持和帮助",category:"支持他人",readTime:"6分钟",color:"from-orange-500 to-red-500",content:`
      当身边有人患抑郁症时，你的支持非常重要：

      倾听和理解：
      • 耐心倾听，不要急于给建议
      • 避免说"想开点"这样的话
      • 承认他们的感受是真实的
      • 不要试图"修复"他们

      实际支持：
      • 陪伴就医
      • 帮助日常事务
      • 保持定期联系
      • 鼓励专业治疗

      照顾自己：
      • 设定界限
      • 寻求支持
      • 了解抑郁症知识
      • 保持自己的心理健康

      记住，你不能"治愈"别人的抑郁症，但你的支持和陪伴是珍贵的。
    `}],y=[{myth:"抑郁症就是想太多",fact:"抑郁症是一种真实的医学疾病，涉及大脑化学物质的变化"},{myth:"抑郁症患者都很脆弱",fact:"抑郁症可以影响任何人，包括非常坚强的人"},{myth:"抑郁症会自己好起来",fact:"抑郁症需要专业治疗，不会自动消失"},{myth:"药物治疗会让人上瘾",fact:"抗抑郁药物不会成瘾，但需要在医生指导下使用"}];function f(){return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-red-50",children:(0,r.jsxs)("div",{className:"mx-auto max-w-6xl px-6 py-8",children:[(0,r.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:(0,r.jsx)(h.Zp,{className:"mb-8",children:(0,r.jsxs)(h.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,r.jsx)(h.ZB,{className:"text-3xl",children:"科普知识"}),(0,r.jsx)(h.BT,{className:"text-lg",children:"权威的抑郁症科普内容，帮助您正确认识和理解心理健康"})]})})}),(0,r.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"mb-8",children:(0,r.jsx)(h.Zp,{className:"bg-gradient-to-r from-orange-600 to-red-600 text-white",children:(0,r.jsx)(h.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-3xl font-bold mb-2",children:"3.5亿+"}),(0,r.jsx)("div",{className:"text-orange-100",children:"全球抑郁症患者"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-3xl font-bold mb-2",children:"1/4"}),(0,r.jsx)("div",{className:"text-orange-100",children:"女性患病率"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-3xl font-bold mb-2",children:"90%"}),(0,r.jsx)("div",{className:"text-orange-100",children:"治疗有效率"})]})]})})})}),(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"科普文章"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:u.map((e,t)=>(0,r.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},children:(0,r.jsxs)(h.Zp,{className:"h-full hover:shadow-lg transition-shadow cursor-pointer group",children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r ${e.color}`,children:e.category}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:e.readTime})]}),(0,r.jsx)(h.ZB,{className:"group-hover:text-orange-600 transition-colors",children:e.title}),(0,r.jsx)(h.BT,{children:e.description})]}),(0,r.jsxs)(h.Wu,{children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 mb-4 line-clamp-3",children:[e.content.substring(0,150),"..."]}),(0,r.jsxs)(p.$,{variant:"outline",size:"sm",className:"group-hover:border-orange-500 group-hover:text-orange-600",children:["阅读更多 ",(0,r.jsx)(n.A,{className:"w-3 h-3 ml-1"})]})]})]})},e.id))})]}),(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"常见误区澄清"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:y.map((e,t)=>(0,r.jsx)(a.P.div,{initial:{opacity:0,x:t%2==0?-20:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.1*t},children:(0,r.jsx)(h.Zp,{children:(0,r.jsx)(h.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(l,{className:"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-red-700 mb-1",children:"误区"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.myth})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-green-700 mb-1",children:"事实"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.fact})]})]})]})})})},t))})]}),(0,r.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},children:(0,r.jsx)(h.Zp,{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,r.jsx)(h.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(o.A,{className:"w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"需要帮助？"}),(0,r.jsx)("p",{className:"text-lg mb-6 text-blue-100",children:"如果您或您身边的人正在经历抑郁症状，请不要犹豫寻求专业帮助"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(p.$,{size:"lg",variant:"secondary",asChild:!0,children:(0,r.jsxs)(m(),{href:"/assessment",className:"flex items-center space-x-2",children:[(0,r.jsx)(c.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"开始评估"})]})}),(0,r.jsx)(p.$,{size:"lg",variant:"outline",className:"text-white border-white hover:bg-white hover:text-blue-600",asChild:!0,children:(0,r.jsx)(m(),{href:"/chat",children:"AI陪聊"})})]})]})})})})]})})}},79551:e=>{"use strict";e.exports=require("url")},90783:(e,t,s)=>{Promise.resolve().then(s.bind(s,22185))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,527,820,725],()=>s(7327));module.exports=r})();