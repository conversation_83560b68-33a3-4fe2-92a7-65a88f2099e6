(()=>{var e={};e.id=335,e.ids=[335],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27900:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},28638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(60687),a=r(43210),l=r(21121),i=r(62688);let n=(0,i.A)("stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]),o=(0,i.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),c=(0,i.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),d=(0,i.A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]);var m=r(5336),p=r(41312),x=r(67760),u=r(27900),h=r(29523),f=r(44493);let y=[{icon:n,title:"心理专业支持者",description:"心理学/精神科背景，协助科普内容审核、AI对话引导",skills:["心理学专业","精神科医师","心理咨询师","社会工作者"],color:"from-blue-500 to-cyan-500"},{icon:o,title:"技术开发者",description:"前端/后端/AI开发，构建Web平台和AI陪聊系统",skills:["React/Next.js","Node.js/Python","AI/GPT API","数据库设计"],color:"from-green-500 to-emerald-500"},{icon:c,title:"设计师",description:"视觉设计/UI设计，为应用赋予温暖的视觉体验",skills:["UI/UX设计","品牌设计","插画设计","用户体验"],color:"from-purple-500 to-pink-500"},{icon:d,title:"内容创作者",description:"内容传播/写作/社媒运营，撰写科普文案和项目故事",skills:["内容写作","社媒运营","视频制作","科普传播"],color:"from-orange-500 to-red-500"}];function j(){let[e,t]=(0,a.useState)({name:"",email:"",role:"",skills:"",motivation:"",experience:""}),[r,i]=(0,a.useState)(!1),n=e=>{let{name:r,value:s}=e.target;t(e=>({...e,[r]:s}))};return r?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center",children:(0,s.jsx)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8},className:"max-w-md mx-auto px-6",children:(0,s.jsxs)(f.Zp,{className:"text-center",children:[(0,s.jsxs)(f.aR,{children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"w-8 h-8 text-white"})}),(0,s.jsx)(f.ZB,{className:"text-2xl",children:"申请已提交"}),(0,s.jsx)(f.BT,{children:"感谢您的申请！我们会尽快与您联系。"})]}),(0,s.jsxs)(f.Wu,{children:[(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"您的热情和专业技能对我们来说非常宝贵。我们期待与您一起为心理健康事业贡献力量。"}),(0,s.jsx)(h.$,{onClick:()=>{i(!1),t({name:"",email:"",role:"",skills:"",motivation:"",experience:""})},variant:"outline",className:"w-full",children:"提交另一个申请"})]})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50",children:(0,s.jsxs)("div",{className:"mx-auto max-w-6xl px-6 py-8",children:[(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:(0,s.jsx)(f.Zp,{className:"mb-8",children:(0,s.jsxs)(f.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,s.jsx)(p.A,{className:"w-8 h-8 text-white"})}),(0,s.jsx)(f.ZB,{className:"text-3xl",children:"加入我们"}),(0,s.jsx)(f.BT,{className:"text-lg",children:"我们相信，一点善意能照亮一片黑暗"})]})})}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:(0,s.jsx)(f.Zp,{className:"mb-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,s.jsx)(f.Wu,{className:"pt-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(x.A,{className:"w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"我们的使命"}),(0,s.jsx)("p",{className:"text-lg leading-relaxed max-w-3xl mx-auto",children:'YYZ 是一个以"抑郁症研究与帮助"为核心目标的公益性项目。我们希望以数字技术为桥梁， 让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声， 为困顿者搭建桥梁。'})]})})})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsx)("div",{children:(0,s.jsxs)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"我们需要什么样的你？"}),(0,s.jsx)("div",{className:"space-y-4",children:y.map((e,t)=>{let r=e.icon;return(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},children:(0,s.jsx)(f.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,s.jsx)(f.Wu,{className:"pt-6",children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:`w-12 h-12 rounded-lg bg-gradient-to-r ${e.color} flex items-center justify-center flex-shrink-0`,children:(0,s.jsx)(r,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.description}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.skills.map(e=>(0,s.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:e},e))})]})]})})})},e.title)})})]})}),(0,s.jsx)("div",{children:(0,s.jsx)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.6},children:(0,s.jsxs)(f.Zp,{children:[(0,s.jsxs)(f.aR,{children:[(0,s.jsx)(f.ZB,{children:"志愿者申请表"}),(0,s.jsx)(f.BT,{children:"填写下方表单，让我们认识你！"})]}),(0,s.jsx)(f.Wu,{children:(0,s.jsxs)("form",{onSubmit:t=>{t.preventDefault(),console.log("Form submitted:",e),i(!0)},className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"姓名 *"}),(0,s.jsx)("input",{type:"text",name:"name",value:e.name,onChange:n,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的姓名"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"邮箱 *"}),(0,s.jsx)("input",{type:"email",name:"email",value:e.email,onChange:n,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"<EMAIL>"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"感兴趣的角色 *"}),(0,s.jsxs)("select",{name:"role",value:e.role,onChange:n,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"",children:"请选择角色"}),y.map(e=>(0,s.jsx)("option",{value:e.title,children:e.title},e.title))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"技能和经验"}),(0,s.jsx)("textarea",{name:"skills",value:e.skills,onChange:n,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请简述您的相关技能和经验..."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"参与动机 *"}),(0,s.jsx)("textarea",{name:"motivation",value:e.motivation,onChange:n,required:!0,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"为什么想要加入我们？您希望为这个项目贡献什么？"})]}),(0,s.jsxs)(h.$,{type:"submit",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:[(0,s.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"提交申请"]})]})})]})})})]}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"mt-12",children:(0,s.jsx)(f.Zp,{className:"bg-gray-50",children:(0,s.jsx)(f.Wu,{className:"pt-6",children:(0,s.jsxs)("blockquote",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-xl italic text-gray-700 mb-4",children:'"如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。"'}),(0,s.jsx)("footer",{className:"text-gray-500",children:"— YYZ 项目发起人"})]})})})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=r(60687),a=r(43210),l=r(4780);let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...t}));n.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},57099:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let c={children:["",{children:["join",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58395)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/join/page",pathname:"/join",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},58395:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx","default")},59047:(e,t,r)=>{Promise.resolve().then(r.bind(r,28638))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},99215:(e,t,r)=>{Promise.resolve().then(r.bind(r,58395))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,527,820,725],()=>r(57099));module.exports=s})();