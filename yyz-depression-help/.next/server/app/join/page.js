(()=>{var e={};e.id=335,e.ids=[335],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27900:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n});var t=r(60687),a=r(43210),l=r(4780);let i=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let n=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let c=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},50672:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(60687),a=r(43210),l=r(21121),i=r(62688);let n=(0,i.A)("stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]),c=(0,i.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),d=(0,i.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),o=(0,i.A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]);var x=r(5336),m=r(41312),p=r(67760),h=r(27900);let u=(0,i.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),j=(0,i.A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]]);var y=r(33872),g=r(29523),f=r(44493);let b=[{icon:n,title:"心理专业支持者",description:"心理学/精神科背景，协助科普内容审核、AI对话引导",skills:["心理学专业","精神科医师","心理咨询师","社会工作者"],color:"from-blue-500 to-cyan-500"},{icon:c,title:"技术开发者",description:"前端/后端/AI开发，构建Web平台和AI陪聊系统",skills:["React/Next.js","Node.js/Python","AI/GPT API","数据库设计"],color:"from-green-500 to-emerald-500"},{icon:d,title:"设计师",description:"视觉设计/UI设计，为应用赋予温暖的视觉体验",skills:["UI/UX设计","品牌设计","插画设计","用户体验"],color:"from-purple-500 to-pink-500"},{icon:o,title:"内容创作者",description:"内容传播/写作/社媒运营，撰写科普文案和项目故事",skills:["内容写作","社媒运营","视频制作","科普传播"],color:"from-orange-500 to-red-500"}];function N(){let[e,s]=(0,a.useState)({name:"",email:"",role:"",skills:"",motivation:"",experience:""}),[r,i]=(0,a.useState)(!1),n=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t}))};return r?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center",children:(0,t.jsx)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8},className:"max-w-md mx-auto px-6",children:(0,t.jsxs)(f.Zp,{className:"text-center",children:[(0,t.jsxs)(f.aR,{children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center",children:(0,t.jsx)(x.A,{className:"w-8 h-8 text-white"})}),(0,t.jsx)(f.ZB,{className:"text-2xl",children:"申请已提交"}),(0,t.jsx)(f.BT,{children:"感谢您的申请！我们会尽快与您联系。"})]}),(0,t.jsxs)(f.Wu,{children:[(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"您的热情和专业技能对我们来说非常宝贵。我们期待与您一起为心理健康事业贡献力量。"}),(0,t.jsx)(g.$,{onClick:()=>{i(!1),s({name:"",email:"",role:"",skills:"",motivation:"",experience:""})},variant:"outline",className:"w-full",children:"提交另一个申请"})]})]})})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50",children:(0,t.jsxs)("div",{className:"mx-auto max-w-6xl px-6 py-8",children:[(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:(0,t.jsx)(f.Zp,{className:"mb-8",children:(0,t.jsxs)(f.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,t.jsx)(m.A,{className:"w-8 h-8 text-white"})}),(0,t.jsx)(f.ZB,{className:"text-3xl",children:"加入我们"}),(0,t.jsx)(f.BT,{className:"text-lg",children:"我们相信，一点善意能照亮一片黑暗"})]})})}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:(0,t.jsx)(f.Zp,{className:"mb-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,t.jsx)(f.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(p.A,{className:"w-12 h-12 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"我们的使命"}),(0,t.jsx)("p",{className:"text-lg leading-relaxed max-w-3xl mx-auto",children:'YYZ 是一个以"抑郁症研究与帮助"为核心目标的公益性项目。我们希望以数字技术为桥梁， 让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声， 为困顿者搭建桥梁。'})]})})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsx)("div",{children:(0,t.jsxs)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"我们需要什么样的你？"}),(0,t.jsx)("div",{className:"space-y-4",children:b.map((e,s)=>{let r=e.icon;return(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*s},children:(0,t.jsx)(f.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,t.jsx)(f.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:`w-12 h-12 rounded-lg bg-gradient-to-r ${e.color} flex items-center justify-center flex-shrink-0`,children:(0,t.jsx)(r,{className:"w-6 h-6 text-white"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.description}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.skills.map(e=>(0,t.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:e},e))})]})]})})})},e.title)})})]})}),(0,t.jsx)("div",{children:(0,t.jsx)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.6},children:(0,t.jsxs)(f.Zp,{children:[(0,t.jsxs)(f.aR,{children:[(0,t.jsx)(f.ZB,{children:"志愿者申请表"}),(0,t.jsx)(f.BT,{children:"填写下方表单，让我们认识你！"})]}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("form",{onSubmit:s=>{s.preventDefault(),console.log("Form submitted:",e),i(!0)},className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"姓名 *"}),(0,t.jsx)("input",{type:"text",name:"name",value:e.name,onChange:n,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的姓名"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"邮箱 *"}),(0,t.jsx)("input",{type:"email",name:"email",value:e.email,onChange:n,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"感兴趣的角色 *"}),(0,t.jsxs)("select",{name:"role",value:e.role,onChange:n,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,t.jsx)("option",{value:"",children:"请选择角色"}),b.map(e=>(0,t.jsx)("option",{value:e.title,children:e.title},e.title))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"技能和经验"}),(0,t.jsx)("textarea",{name:"skills",value:e.skills,onChange:n,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请简述您的相关技能和经验..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"参与动机 *"}),(0,t.jsx)("textarea",{name:"motivation",value:e.motivation,onChange:n,required:!0,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"为什么想要加入我们？您希望为这个项目贡献什么？"})]}),(0,t.jsxs)(g.$,{type:"submit",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"提交申请"]})]})})]})})})]}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"mt-12",children:(0,t.jsxs)(f.Zp,{children:[(0,t.jsxs)(f.aR,{className:"text-center",children:[(0,t.jsxs)(f.ZB,{className:"text-2xl flex items-center justify-center space-x-2",children:[(0,t.jsx)(u,{className:"w-6 h-6"}),(0,t.jsx)("span",{children:"社区事务论坛"})]}),(0,t.jsx)(f.BT,{children:"项目公告、用户反馈与社区交流平台"})]}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center space-x-2",children:[(0,t.jsx)(j,{className:"w-5 h-5 text-blue-600"}),(0,t.jsx)("span",{children:"项目公告"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 border border-blue-200 rounded-lg bg-blue-50",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"YYZ平台正式上线"}),(0,t.jsx)("span",{className:"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded",children:"置顶"})]}),(0,t.jsx)("p",{className:"text-blue-700 text-sm mb-2",children:"经过团队的不懈努力，YYZ抑郁症研究与帮助平台正式上线！欢迎大家体验各项功能。"}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-blue-600",children:[(0,t.jsx)("span",{children:"发布者：YYZ团队"}),(0,t.jsx)("span",{children:"2024-06-05"})]})]}),(0,t.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"志愿者招募计划启动"}),(0,t.jsx)("p",{className:"text-gray-700 text-sm mb-2",children:"我们正在招募心理专业支持者、技术开发者、设计师等各类志愿者，欢迎加入！"}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,t.jsx)("span",{children:"发布者：人事部"}),(0,t.jsx)("span",{children:"2024-06-03"})]})]}),(0,t.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"功能更新说明"}),(0,t.jsx)("p",{className:"text-gray-700 text-sm mb-2",children:"新增医疗数据管理功能，用户可以更好地管理健康记录和生成医疗报告。"}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,t.jsx)("span",{children:"发布者：技术部"}),(0,t.jsx)("span",{children:"2024-06-01"})]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"w-5 h-5 text-green-600"}),(0,t.jsx)("span",{children:"用户反馈"})]}),(0,t.jsxs)("div",{className:"mb-6 p-4 border border-green-200 rounded-lg bg-green-50",children:[(0,t.jsx)("h4",{className:"font-medium text-green-900 mb-3",children:"提交反馈"}),(0,t.jsxs)("form",{className:"space-y-3",children:[(0,t.jsx)("div",{children:(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-green-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm",children:[(0,t.jsx)("option",{value:"",children:"选择反馈类型"}),(0,t.jsx)("option",{value:"bug",children:"Bug报告"}),(0,t.jsx)("option",{value:"feature",children:"功能建议"}),(0,t.jsx)("option",{value:"improvement",children:"改进建议"}),(0,t.jsx)("option",{value:"other",children:"其他"})]})}),(0,t.jsx)("div",{children:(0,t.jsx)("textarea",{rows:3,className:"w-full px-3 py-2 border border-green-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm",placeholder:"请详细描述您的反馈..."})}),(0,t.jsxs)(g.$,{size:"sm",className:"w-full bg-green-600 hover:bg-green-700",children:[(0,t.jsx)(h.A,{className:"w-3 h-3 mr-2"}),"提交反馈"]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"最近反馈"}),(0,t.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded",children:"功能建议"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"2天前"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:"希望能增加情绪日志的导出功能，方便与医生分享。"}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["状态：",(0,t.jsx)("span",{className:"text-green-600",children:"已采纳"})]})]}),(0,t.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded",children:"改进建议"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"5天前"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:"AI陪聊的回复速度可以再快一些。"}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["状态：",(0,t.jsx)("span",{className:"text-yellow-600",children:"处理中"})]})]}),(0,t.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-xs bg-red-100 text-red-700 px-2 py-1 rounded",children:"Bug报告"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"1周前"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:"移动端情绪日志页面显示异常。"}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["状态：",(0,t.jsx)("span",{className:"text-green-600",children:"已修复"})]})]})]})]})]})})]})}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},className:"mt-12",children:(0,t.jsx)(f.Zp,{className:"bg-gray-50",children:(0,t.jsx)(f.Wu,{className:"pt-6",children:(0,t.jsxs)("blockquote",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-xl italic text-gray-700 mb-4",children:'"                  "如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。""'}),(0,t.jsx)("footer",{className:"text-gray-500",children:"— YYZ 项目发起人"})]})})})})]})})}},57099:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(s,c);let d={children:["",{children:["join",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58395)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/join/page",pathname:"/join",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},58395:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/join/page.tsx","default")},59047:(e,s,r)=>{Promise.resolve().then(r.bind(r,50672))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},99215:(e,s,r)=>{Promise.resolve().then(r.bind(r,58395))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,242,820,725],()=>r(57099));module.exports=t})();