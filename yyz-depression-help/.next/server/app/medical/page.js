(()=>{var e={};e.id=202,e.ids=[202],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13342:(e,s,t)=>{Promise.resolve().then(t.bind(t,99524))},13377:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(60687),r=t(43210),l=t(21121),i=t(10022),n=t(62688);let c=(0,n.A)("pill",[["path",{d:"m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z",key:"wa1lgi"}],["path",{d:"m8.5 8.5 7 7",key:"rvfmvr"}]]);var d=t(58869),o=t(99891),x=t(96474);let m=(0,n.A)("pen-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]),p=(0,n.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var h=t(67760),u=t(93613),j=t(29523),f=t(44493);let y=[{id:"1",type:"assessment",date:"2024-06-05",title:"PHQ-9 抑郁症评估",content:"总分：12分，中度抑郁症状",score:12},{id:"2",type:"medication",date:"2024-06-03",title:"用药记录",content:"舍曲林 50mg，每日一次，晨服"},{id:"3",type:"consultation",date:"2024-06-01",title:"心理咨询",content:"认知行为疗法，第3次会谈，情绪有所改善"}];function v(){let[e]=(0,r.useState)(y),[s,t]=(0,r.useState)(!1),[n,v]=(0,r.useState)("medication"),g=e=>{switch(e){case"assessment":default:return i.A;case"medication":return c;case"consultation":return d.A}},b=e=>{switch(e){case"assessment":return"from-blue-500 to-cyan-500";case"medication":return"from-green-500 to-emerald-500";case"consultation":return"from-purple-500 to-pink-500";default:return"from-gray-500 to-gray-600"}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-50",children:(0,a.jsxs)("div",{className:"mx-auto max-w-6xl px-6 py-8",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:(0,a.jsx)(f.Zp,{className:"mb-8",children:(0,a.jsxs)(f.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)(f.ZB,{className:"text-2xl",children:"医疗数据与导出"}),(0,a.jsx)(f.BT,{children:"管理您的健康记录，生成专业医疗报告"})]})})}),(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"mb-8",children:(0,a.jsx)(f.Zp,{className:"bg-blue-50 border-blue-200",children:(0,a.jsx)(f.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-800 mb-1",children:"隐私保护"}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"您的所有医疗数据都存储在本地设备中，我们不会上传或分享您的个人健康信息。 导出的PDF报告仅供您个人使用或与医生分享。"})]})]})})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"健康记录"}),(0,a.jsxs)(j.$,{onClick:()=>t(!s),className:"bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"添加记录"]})]}),s&&(0,a.jsxs)(f.Zp,{className:"mb-6",children:[(0,a.jsx)(f.aR,{children:(0,a.jsx)(f.ZB,{className:"text-lg",children:"添加新记录"})}),(0,a.jsx)(f.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"记录类型"}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"medication",checked:"medication"===n,onChange:e=>v(e.target.value),className:"mr-2"}),"用药记录"]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"consultation",checked:"consultation"===n,onChange:e=>v(e.target.value),className:"mr-2"}),"咨询记录"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"日期"}),(0,a.jsx)("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",defaultValue:new Date().toISOString().split("T")[0]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"medication"===n?"药物名称和剂量":"咨询内容"}),(0,a.jsx)("textarea",{rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",placeholder:"medication"===n?"例如：舍曲林 50mg，每日一次":"例如：认知行为疗法，第1次会谈"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(j.$,{onClick:()=>{alert("记录已保存（Demo版本）"),t(!1)},className:"bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700",children:"保存记录"}),(0,a.jsx)(j.$,{onClick:()=>t(!1),variant:"outline",children:"取消"})]})]})})]}),(0,a.jsx)("div",{className:"space-y-4",children:e.map((e,s)=>{let t=g(e.type);return(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*s},children:(0,a.jsx)(f.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,a.jsx)(f.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:`w-12 h-12 rounded-lg bg-gradient-to-r ${b(e.type)} flex items-center justify-center flex-shrink-0`,children:(0,a.jsx)(t,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:e.content}),(0,a.jsx)("p",{className:"text-gray-500 text-xs",children:e.date})]}),(0,a.jsx)(j.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(m,{className:"w-4 h-4"})})]})})]})})})},e.id)})})]})}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.6},className:"space-y-6",children:[(0,a.jsxs)(f.Zp,{children:[(0,a.jsxs)(f.aR,{children:[(0,a.jsxs)(f.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(p,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"导出报告"})]}),(0,a.jsx)(f.BT,{children:"生成专业的健康概览报告"})]}),(0,a.jsx)(f.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(j.$,{onClick:()=>{alert("PDF报告生成中...（Demo版本）")},className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",children:[(0,a.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"生成PDF报告"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("p",{className:"mb-2",children:"报告将包含："}),(0,a.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,a.jsx)("li",{children:"• 评估结果趋势图"}),(0,a.jsx)("li",{children:"• 用药记录时间线"}),(0,a.jsx)("li",{children:"• 咨询会谈总结"}),(0,a.jsx)("li",{children:"• 情绪变化分析"})]})]})]})})]}),(0,a.jsxs)(f.Zp,{children:[(0,a.jsx)(f.aR,{children:(0,a.jsxs)(f.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"健康统计"})]})}),(0,a.jsx)(f.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"评估次数"}),(0,a.jsx)("span",{className:"font-semibold",children:"1"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"用药记录"}),(0,a.jsx)("span",{className:"font-semibold",children:"1"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"咨询次数"}),(0,a.jsx)("span",{className:"font-semibold",children:"1"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"最近评估"}),(0,a.jsx)("span",{className:"font-semibold text-orange-600",children:"中度"})]})]})})]}),(0,a.jsx)(f.Zp,{className:"bg-yellow-50 border-yellow-200",children:(0,a.jsx)(f.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-yellow-800 mb-1",children:"重要提醒"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700",children:"本功能生成的报告仅供参考，不能替代专业医疗诊断。 请将报告作为与医生沟通的辅助工具。"})]})]})})})]})})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25379:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["medical",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,99524)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/medical/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/medical/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/medical/page",pathname:"/medical",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n});var a=t(60687),r=t(43210),l=t(4780);let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},53510:(e,s,t)=>{Promise.resolve().then(t.bind(t,13377))},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99524:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/medical/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/medical/page.tsx","default")},99891:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,242,820,725],()=>t(25379));module.exports=a})();