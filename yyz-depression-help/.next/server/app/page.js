(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5475:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21204)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},15807:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/app/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>n});var r=s(60687),a=s(43210),i=s(4780);let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));l.displayName="Card";let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));n.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65724:(e,t,s)=>{Promise.resolve().then(s.bind(s,21204))},70334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},75452:(e,t,s)=>{Promise.resolve().then(s.bind(s,75694))},75694:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(60687),a=s(21121),i=s(78200),l=s(33872),n=s(40228),o=s(82080),c=s(10022),d=s(99891),x=s(11437),m=s(15807),p=s(67760),h=s(70334),u=s(41312),g=s(85814),y=s.n(g),f=s(29523),j=s(44493);let v=[{icon:i.A,title:"心理评估",description:"基于PHQ-9量表的专业抑郁症评估工具，帮助您了解自己的心理状态",href:"/assessment",color:"from-blue-500 to-cyan-500"},{icon:l.A,title:"AI陪聊",description:"24小时温暖陪伴，AI数字伙伴倾听您的心声，提供情感支持",href:"/chat",color:"from-purple-500 to-pink-500"},{icon:n.A,title:"情绪日志",description:"记录每日情绪变化，通过数据可视化了解自己的情绪模式",href:"/mood",color:"from-green-500 to-emerald-500"},{icon:o.A,title:"科普知识",description:"权威的抑郁症科普内容，帮助您正确认识和理解心理健康",href:"/education",color:"from-orange-500 to-red-500"},{icon:c.A,title:"医疗数据",description:"管理健康记录，生成专业医疗报告，便于与医生沟通",href:"/medical",color:"from-teal-500 to-cyan-500"}],b=[{number:"3.5亿+",label:"全球抑郁症患者"},{number:"24/7",label:"全天候陪伴"},{number:"100%",label:"匿名保护"},{number:"0元",label:"完全免费"}],w=[{icon:d.A,title:"隐私保护",description:"您的所有数据都受到严格保护，支持匿名使用"},{icon:x.A,title:"开放合作",description:"与医疗机构、高校、公益组织共同构建支持网络"},{icon:m.A,title:"科学专业",description:"基于循证医学和心理学研究的专业工具和内容"}];function N(){return(0,r.jsxs)("div",{className:"min-h-screen",children:[(0,r.jsxs)("section",{className:"relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,r.jsx)("div",{className:"relative mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8",children:(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"mx-auto max-w-4xl text-center",children:[(0,r.jsx)(a.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,delay:.2},className:"mb-8 flex justify-center",children:(0,r.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg",children:(0,r.jsx)(p.A,{className:"w-10 h-10 text-white"})})}),(0,r.jsxs)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl",children:[(0,r.jsx)("span",{className:"block",children:"愿所有被困在夜里的灵魂"}),(0,r.jsx)("span",{className:"block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"都能看到清晨的第一缕光"})]}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto",children:'YYZ是一个以"抑郁症研究与帮助"为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。'}),(0,r.jsxs)("div",{className:"mt-10 flex items-center justify-center gap-x-6",children:[(0,r.jsx)(f.$,{size:"lg",variant:"warm",asChild:!0,children:(0,r.jsxs)(y(),{href:"/assessment",className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:"开始评估"}),(0,r.jsx)(h.A,{className:"w-4 h-4"})]})}),(0,r.jsx)(f.$,{size:"lg",variant:"outline",asChild:!0,children:(0,r.jsx)(y(),{href:"/chat",children:"AI陪聊"})})]})]})})]}),(0,r.jsx)("section",{className:"py-16 bg-white",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:(0,r.jsx)("div",{className:"grid grid-cols-2 gap-8 md:grid-cols-4",children:b.map((e,t)=>(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.number}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e.label})]},e.label))})})}),(0,r.jsx)("section",{className:"py-24 bg-gray-50",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mx-auto max-w-2xl text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"我们的核心功能"}),(0,r.jsx)("p",{className:"mt-4 text-lg leading-8 text-gray-600",children:"为您提供全方位的心理健康支持服务"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",children:v.map((e,t)=>{let s=e.icon;return(0,r.jsx)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},children:(0,r.jsxs)(j.Zp,{className:"h-full hover:shadow-lg transition-shadow cursor-pointer group",children:[(0,r.jsxs)(j.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:`w-12 h-12 mx-auto mb-4 rounded-lg bg-gradient-to-r ${e.color} flex items-center justify-center group-hover:scale-110 transition-transform`,children:(0,r.jsx)(s,{className:"w-6 h-6 text-white"})}),(0,r.jsx)(j.ZB,{className:"text-xl",children:e.title})]}),(0,r.jsxs)(j.Wu,{children:[(0,r.jsx)(j.BT,{className:"text-center text-gray-600",children:e.description}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)(f.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsx)(y(),{href:e.href,children:"了解更多"})})})]})]})},e.title)})})]})}),(0,r.jsx)("section",{className:"py-24 bg-white",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mx-auto max-w-2xl text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:"我们的价值理念"}),(0,r.jsx)("p",{className:"mt-4 text-lg leading-8 text-gray-600",children:"以人为本，科技赋能，构建温暖的数字心理健康生态"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:w.map((e,t)=>{let s=e.icon;return(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2*t},viewport:{once:!0},className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(s,{className:"w-8 h-8 text-white"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600",children:e.description})]},e.title)})})]})}),(0,r.jsx)("section",{className:"py-24 bg-gradient-to-r from-blue-600 to-purple-600",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mx-auto max-w-2xl text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-white sm:text-4xl",children:"加入我们，一起点亮希望"}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-blue-100",children:"如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。"}),(0,r.jsxs)("div",{className:"mt-10 flex items-center justify-center gap-x-6",children:[(0,r.jsx)(f.$,{size:"lg",variant:"secondary",asChild:!0,children:(0,r.jsxs)(y(),{href:"/join",className:"flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"加入我们"})]})}),(0,r.jsx)(f.$,{size:"lg",variant:"outline",className:"text-white border-white hover:bg-white hover:text-blue-600",asChild:!0,children:(0,r.jsx)(y(),{href:"/education",children:"了解更多"})})]})]})})}),(0,r.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"flex justify-center items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"YYZ"})]}),(0,r.jsx)("p",{className:"text-gray-400 mb-4",children:"抑郁症研究与帮助 - 愿所有被困在夜里的灵魂，都能看到清晨的第一缕光"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"\xa9 2024 YYZ Project. 本平台仅供参考，不能替代专业医疗建议。"})]})})})]})}},79551:e=>{"use strict";e.exports=require("url")},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,242,820,725],()=>s(5475));module.exports=r})();