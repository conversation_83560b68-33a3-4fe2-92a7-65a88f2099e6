exports.id=725,exports.ids=[725],exports.modules={4780:(e,r,t)=>{"use strict";t.d(r,{$C:()=>m,$Z:()=>n,C1:()=>l,IG:()=>h,Yq:()=>c,_o:()=>d,aX:()=>a,cn:()=>o});var i=t(49384),s=t(82348);function o(...e){return(0,s.QP)((0,i.$)(e))}let n=[{id:1,question:"做事时提不起劲或没有兴趣",description:"在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"},{id:2,question:"感到心情低落、沮丧或绝望",description:"在过去两周内，您有多少天感到心情低落、沮丧或绝望？"},{id:3,question:"入睡困难、睡不安稳或睡眠过多",description:"在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"},{id:4,question:"感觉疲倦或没有活力",description:"在过去两周内，您有多少天感觉疲倦或没有活力？"},{id:5,question:"食欲不振或吃太多",description:"在过去两周内，您有多少天有食欲不振或吃太多的问题？"},{id:6,question:"觉得自己很糟糕或觉得自己很失败",description:"在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"},{id:7,question:"对事物专注有困难",description:"在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"},{id:8,question:"动作或说话速度缓慢或烦躁不安",description:"在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"},{id:9,question:"有不如死掉或伤害自己的念头",description:"在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"}],l=[{value:0,label:"完全没有",days:"0天"},{value:1,label:"几天",days:"1-6天"},{value:2,label:"一半以上的天数",days:"7-11天"},{value:3,label:"几乎每天",days:"12-14天"}];function a(e){return e<=4?{level:"minimal",title:"轻微或无抑郁症状",description:"您目前的症状较轻微，建议继续保持良好的生活习惯。",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"}:e<=9?{level:"mild",title:"轻度抑郁症状",description:"您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}:e<=14?{level:"moderate",title:"中度抑郁症状",description:"您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"}:e<=19?{level:"moderately-severe",title:"中重度抑郁症状",description:"您的症状较为严重，强烈建议寻求专业的心理健康服务。",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}:{level:"severe",title:"重度抑郁症状",description:"您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",color:"text-red-800",bgColor:"bg-red-100",borderColor:"border-red-300"}}let d=[{id:"very-happy",label:"非常开心",emoji:"\uD83D\uDE04",color:"#10b981"},{id:"happy",label:"开心",emoji:"\uD83D\uDE0A",color:"#34d399"},{id:"neutral",label:"平静",emoji:"\uD83D\uDE10",color:"#6b7280"},{id:"sad",label:"难过",emoji:"\uD83D\uDE22",color:"#f59e0b"},{id:"very-sad",label:"非常难过",emoji:"\uD83D\uDE2D",color:"#ef4444"},{id:"anxious",label:"焦虑",emoji:"\uD83D\uDE30",color:"#8b5cf6"},{id:"angry",label:"愤怒",emoji:"\uD83D\uDE20",color:"#dc2626"},{id:"tired",label:"疲惫",emoji:"\uD83D\uDE34",color:"#64748b"}];function c(e){return e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}function m(){return Math.random().toString(36).substr(2,9)}let h={get:e=>null,set:(e,r)=>{},remove:e=>{}}},14246:(e,r,t)=>{"use strict";t.d(r,{default:()=>v});var i=t(60687),s=t(43210),o=t(85814),n=t.n(o),l=t(67760),a=t(82080),d=t(78200),c=t(33872),m=t(40228),h=t(10022),b=t(41312),u=t(12941),x=t(11860),f=t(29523),p=t(4780);let g=[{name:"首页",href:"/",icon:l.A},{name:"科普知识",href:"/education",icon:a.A},{name:"心理评估",href:"/assessment",icon:d.A},{name:"AI陪聊",href:"/chat",icon:c.A},{name:"情绪日志",href:"/mood",icon:m.A},{name:"医疗数据",href:"/medical",icon:h.A},{name:"关于我们",href:"/about",icon:b.A},{name:"加入我们",href:"/join",icon:b.A}];function v(){let[e,r]=(0,s.useState)(!1);return(0,i.jsxs)("header",{className:"bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50",children:[(0,i.jsxs)("nav",{className:"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8","aria-label":"Global",children:[(0,i.jsx)("div",{className:"flex lg:flex-1",children:(0,i.jsxs)(n(),{href:"/",className:"-m-1.5 p-1.5 flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,i.jsx)(l.A,{className:"w-5 h-5 text-white"})}),(0,i.jsx)("span",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"YYZ"})]})}),(0,i.jsx)("div",{className:"flex lg:hidden",children:(0,i.jsxs)(f.$,{variant:"ghost",size:"icon",onClick:()=>r(!0),className:"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700",children:[(0,i.jsx)("span",{className:"sr-only",children:"打开主菜单"}),(0,i.jsx)(u.A,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,i.jsx)("div",{className:"hidden lg:flex lg:gap-x-8",children:g.map(e=>{let r=e.icon;return(0,i.jsxs)(n(),{href:e.href,className:"flex items-center space-x-1 text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors",children:[(0,i.jsx)(r,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:e.name})]},e.name)})}),(0,i.jsx)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end",children:(0,i.jsx)(f.$,{variant:"warm",size:"sm",children:"立即开始"})})]}),(0,i.jsx)("div",{className:(0,p.cn)("lg:hidden",e?"fixed inset-0 z-50":"hidden"),children:(0,i.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)(n(),{href:"/",className:"-m-1.5 p-1.5 flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,i.jsx)(l.A,{className:"w-5 h-5 text-white"})}),(0,i.jsx)("span",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"YYZ"})]}),(0,i.jsxs)(f.$,{variant:"ghost",size:"icon",onClick:()=>r(!1),className:"-m-2.5 rounded-md p-2.5 text-gray-700",children:[(0,i.jsx)("span",{className:"sr-only",children:"关闭菜单"}),(0,i.jsx)(x.A,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,i.jsx)("div",{className:"mt-6 flow-root",children:(0,i.jsxs)("div",{className:"-my-6 divide-y divide-gray-500/10",children:[(0,i.jsx)("div",{className:"space-y-2 py-6",children:g.map(e=>{let t=e.icon;return(0,i.jsxs)(n(),{href:e.href,onClick:()=>r(!1),className:"-mx-3 flex items-center space-x-3 rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50",children:[(0,i.jsx)(t,{className:"w-5 h-5"}),(0,i.jsx)("span",{children:e.name})]},e.name)})}),(0,i.jsx)("div",{className:"py-6",children:(0,i.jsx)(f.$,{variant:"warm",className:"w-full",children:"立即开始"})})]})})]})})]})}},22370:(e,r,t)=>{Promise.resolve().then(t.bind(t,14246))},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>a});var i=t(60687),s=t(43210),o=t(24224),n=t(4780);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",warm:"bg-gradient-to-r from-orange-400 to-pink-400 text-white hover:from-orange-500 hover:to-pink-500",calm:"bg-gradient-to-r from-blue-400 to-purple-400 text-white hover:from-blue-500 hover:to-purple-500"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),a=s.forwardRef(({className:e,variant:r,size:t,...s},o)=>(0,i.jsx)("button",{className:(0,n.cn)(l({variant:r,size:t,className:e})),ref:o,...s}));a.displayName="Button"},45732:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},53348:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},61135:()=>{},64544:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});let i=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/抑郁症研究与帮助/yyz-depression-help/src/components/navigation.tsx","default")},82202:(e,r,t)=>{Promise.resolve().then(t.bind(t,64544))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>l});var i=t(37413),s=t(31001),o=t.n(s);t(61135);var n=t(64544);let l={title:"YYZ - 抑郁症研究与帮助",description:"YYZ是一个以抑郁症研究与帮助为核心的公益平台，提供心理评估、AI陪聊、情绪日志等功能，为每一个需要帮助的人点亮希望之光。",keywords:"抑郁症,心理健康,心理评估,AI陪聊,情绪日志,公益平台",authors:[{name:"YYZ Team"}],openGraph:{title:"YYZ - 抑郁症研究与帮助",description:"愿所有被困在夜里的灵魂，都能看到清晨的第一缕光",type:"website"}};function a({children:e}){return(0,i.jsx)("html",{lang:"zh-CN",children:(0,i.jsxs)("body",{className:`${o().variable} font-sans antialiased`,children:[(0,i.jsx)(n.default,{}),(0,i.jsx)("main",{children:e})]})})}}};