{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%8A%91%E9%83%81%E7%97%87%E7%A0%94%E7%A9%B6%E4%B8%8E%E5%B8%AE%E5%8A%A9/yyz-depression-help/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%8A%91%E9%83%81%E7%97%87%E7%A0%94%E7%A9%B6%E4%B8%8E%E5%B8%AE%E5%8A%A9/yyz-depression-help/src/app/education/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { BookOpen, Heart, Brain, AlertCircle, CheckCircle, ArrowRight } from 'lucide-react'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\n\nconst articles = [\n  {\n    id: 1,\n    title: '什么是抑郁症？',\n    description: '了解抑郁症的基本概念、症状表现和常见误区',\n    category: '基础知识',\n    readTime: '5分钟',\n    color: 'from-blue-500 to-cyan-500',\n    content: `\n      抑郁症是一种常见的心理疾病，影响着全球超过3.5亿人。它不仅仅是\"心情不好\"，\n      而是一种持续的、严重影响日常生活的情绪障碍。\n\n      主要症状包括：\n      • 持续的悲伤或空虚感\n      • 对平时喜欢的活动失去兴趣\n      • 疲劳和精力不足\n      • 睡眠问题\n      • 食欲变化\n      • 注意力难以集中\n      • 自我价值感低\n      • 有时会有自伤想法\n\n      重要的是要知道，抑郁症是可以治疗的，寻求帮助是勇敢的表现。\n    `\n  },\n  {\n    id: 2,\n    title: '如何识别抑郁症的早期信号',\n    description: '学会识别自己和他人的抑郁症早期症状',\n    category: '识别诊断',\n    readTime: '7分钟',\n    color: 'from-green-500 to-emerald-500',\n    content: `\n      早期识别抑郁症对于及时干预非常重要。以下是一些需要关注的信号：\n\n      情绪变化：\n      • 持续两周以上的低落情绪\n      • 对未来感到绝望\n      • 易怒或焦虑\n      • 情绪波动大\n\n      行为变化：\n      • 社交退缩，避免与人接触\n      • 工作或学习效率下降\n      • 个人卫生习惯改变\n      • 睡眠模式改变\n\n      身体症状：\n      • 不明原因的疲劳\n      • 头痛或身体疼痛\n      • 消化问题\n      • 食欲变化\n\n      如果这些症状持续存在并影响日常生活，建议寻求专业帮助。\n    `\n  },\n  {\n    id: 3,\n    title: '抑郁症的治疗方法',\n    description: '了解现代医学对抑郁症的治疗方案',\n    category: '治疗康复',\n    readTime: '8分钟',\n    color: 'from-purple-500 to-pink-500',\n    content: `\n      抑郁症有多种有效的治疗方法，通常需要个性化的治疗方案：\n\n      心理治疗：\n      • 认知行为疗法(CBT)\n      • 人际关系疗法\n      • 正念疗法\n      • 家庭治疗\n\n      药物治疗：\n      • 抗抑郁药物\n      • 需要专业医生指导\n      • 通常需要4-6周见效\n      • 不要自行停药\n\n      生活方式调整：\n      • 规律运动\n      • 健康饮食\n      • 充足睡眠\n      • 社交支持\n      • 压力管理\n\n      综合治疗效果最佳，重要的是要有耐心，康复是一个过程。\n    `\n  },\n  {\n    id: 4,\n    title: '如何帮助身边的抑郁症患者',\n    description: '学习如何为抑郁症患者提供支持和帮助',\n    category: '支持他人',\n    readTime: '6分钟',\n    color: 'from-orange-500 to-red-500',\n    content: `\n      当身边有人患抑郁症时，你的支持非常重要：\n\n      倾听和理解：\n      • 耐心倾听，不要急于给建议\n      • 避免说\"想开点\"这样的话\n      • 承认他们的感受是真实的\n      • 不要试图\"修复\"他们\n\n      实际支持：\n      • 陪伴就医\n      • 帮助日常事务\n      • 保持定期联系\n      • 鼓励专业治疗\n\n      照顾自己：\n      • 设定界限\n      • 寻求支持\n      • 了解抑郁症知识\n      • 保持自己的心理健康\n\n      记住，你不能\"治愈\"别人的抑郁症，但你的支持和陪伴是珍贵的。\n    `\n  }\n]\n\nconst myths = [\n  {\n    myth: '抑郁症就是想太多',\n    fact: '抑郁症是一种真实的医学疾病，涉及大脑化学物质的变化'\n  },\n  {\n    myth: '抑郁症患者都很脆弱',\n    fact: '抑郁症可以影响任何人，包括非常坚强的人'\n  },\n  {\n    myth: '抑郁症会自己好起来',\n    fact: '抑郁症需要专业治疗，不会自动消失'\n  },\n  {\n    myth: '药物治疗会让人上瘾',\n    fact: '抗抑郁药物不会成瘾，但需要在医生指导下使用'\n  }\n]\n\nexport default function EducationPage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-50 to-red-50\">\n      <div className=\"mx-auto max-w-6xl px-6 py-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <Card className=\"mb-8\">\n            <CardHeader className=\"text-center\">\n              <div className=\"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center\">\n                <BookOpen className=\"w-8 h-8 text-white\" />\n              </div>\n              <CardTitle className=\"text-3xl\">科普知识</CardTitle>\n              <CardDescription className=\"text-lg\">\n                权威的抑郁症科普内容，帮助您正确认识和理解心理健康\n              </CardDescription>\n            </CardHeader>\n          </Card>\n        </motion.div>\n\n        {/* Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"mb-8\"\n        >\n          <Card className=\"bg-gradient-to-r from-orange-600 to-red-600 text-white\">\n            <CardContent className=\"pt-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n                <div>\n                  <div className=\"text-3xl font-bold mb-2\">3.5亿+</div>\n                  <div className=\"text-orange-100\">全球抑郁症患者</div>\n                </div>\n                <div>\n                  <div className=\"text-3xl font-bold mb-2\">1/4</div>\n                  <div className=\"text-orange-100\">女性患病率</div>\n                </div>\n                <div>\n                  <div className=\"text-3xl font-bold mb-2\">90%</div>\n                  <div className=\"text-orange-100\">治疗有效率</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        {/* Articles */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">科普文章</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {articles.map((article, index) => (\n              <motion.div\n                key={article.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 * index }}\n              >\n                <Card className=\"h-full hover:shadow-lg transition-shadow cursor-pointer group\">\n                  <CardHeader>\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className={`px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r ${article.color}`}>\n                        {article.category}\n                      </span>\n                      <span className=\"text-sm text-gray-500\">{article.readTime}</span>\n                    </div>\n                    <CardTitle className=\"group-hover:text-orange-600 transition-colors\">\n                      {article.title}\n                    </CardTitle>\n                    <CardDescription>{article.description}</CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-sm text-gray-600 mb-4 line-clamp-3\">\n                      {article.content.substring(0, 150)}...\n                    </div>\n                    <Button variant=\"outline\" size=\"sm\" className=\"group-hover:border-orange-500 group-hover:text-orange-600\">\n                      阅读更多 <ArrowRight className=\"w-3 h-3 ml-1\" />\n                    </Button>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Myths vs Facts */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          className=\"mb-8\"\n        >\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">常见误区澄清</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {myths.map((item, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 * index }}\n              >\n                <Card>\n                  <CardContent className=\"pt-6\">\n                    <div className=\"space-y-4\">\n                      <div className=\"flex items-start space-x-3\">\n                        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0\" />\n                        <div>\n                          <h4 className=\"font-medium text-red-700 mb-1\">误区</h4>\n                          <p className=\"text-sm text-gray-600\">{item.myth}</p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-start space-x-3\">\n                        <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0\" />\n                        <div>\n                          <h4 className=\"font-medium text-green-700 mb-1\">事实</h4>\n                          <p className=\"text-sm text-gray-600\">{item.fact}</p>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n        >\n          <Card className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n            <CardContent className=\"pt-6\">\n              <div className=\"text-center\">\n                <Heart className=\"w-12 h-12 mx-auto mb-4\" />\n                <h2 className=\"text-2xl font-bold mb-4\">需要帮助？</h2>\n                <p className=\"text-lg mb-6 text-blue-100\">\n                  如果您或您身边的人正在经历抑郁症状，请不要犹豫寻求专业帮助\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                  <Button size=\"lg\" variant=\"secondary\" asChild>\n                    <Link href=\"/assessment\" className=\"flex items-center space-x-2\">\n                      <Brain className=\"w-5 h-5\" />\n                      <span>开始评估</span>\n                    </Link>\n                  </Button>\n                  <Button size=\"lg\" variant=\"outline\" className=\"text-white border-white hover:bg-white hover:text-blue-600\" asChild>\n                    <Link href=\"/chat\">AI陪聊</Link>\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,SAAS,CAAC;;;;;;;;;;;;;;;IAeV,CAAC;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;IAsBV,CAAC;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;IAuBV,CAAC;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;IAsBV,CAAC;IACH;CACD;AAED,MAAM,QAAQ;IACZ;QACE,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAW;;;;;;8CAChC,8OAAC,gIAAA,CAAA,kBAAe;oCAAC,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;8BAQ3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,8OAAC;gDAAI,WAAU;0DAAkB;;;;;;;;;;;;kDAEnC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,8OAAC;gDAAI,WAAU;0DAAkB;;;;;;;;;;;;kDAEnC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,8OAAC;gDAAI,WAAU;0DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAM;8CAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,uEAAuE,EAAE,QAAQ,KAAK,EAAE;0EACvG,QAAQ,QAAQ;;;;;;0EAEnB,8OAAC;gEAAK,WAAU;0EAAyB,QAAQ,QAAQ;;;;;;;;;;;;kEAE3D,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,QAAQ,KAAK;;;;;;kEAEhB,8OAAC,gIAAA,CAAA,kBAAe;kEAAE,QAAQ,WAAW;;;;;;;;;;;;0DAEvC,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG;4DAAK;;;;;;;kEAErC,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;;4DAA4D;0EACnG,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;mCAvB5B,QAAQ,EAAE;;;;;;;;;;;;;;;;8BAiCvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,QAAQ,MAAM,IAAI,CAAC,KAAK;oCAAG;oCACrD,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAM;8CAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;kDACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAgC;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;kFAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;kEAGnD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAkC;;;;;;kFAChD,8OAAC;wEAAE,WAAU;kFAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAnBpD;;;;;;;;;;;;;;;;8BA+Bb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAY,OAAO;0DAC3C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAc,WAAU;;sEACjC,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,WAAU;gDAA6D,OAAO;0DAChH,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC", "debugId": null}}]}