{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%8A%91%E9%83%81%E7%97%87%E7%A0%94%E7%A9%B6%E4%B8%8E%E5%B8%AE%E5%8A%A9/yyz-depression-help/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%8A%91%E9%83%81%E7%97%87%E7%A0%94%E7%A9%B6%E4%B8%8E%E5%B8%AE%E5%8A%A9/yyz-depression-help/src/app/docs/whitepaper/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { BookOpen, ArrowLeft, BarChart3, Users, Shield, Globe } from 'lucide-react'\nimport Link from 'next/link'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\n\nexport default function WhitepaperPage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50\">\n      <div className=\"mx-auto max-w-4xl px-6 py-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <div className=\"mb-6\">\n            <Button variant=\"ghost\" asChild className=\"mb-4\">\n              <Link href=\"/about\" className=\"flex items-center space-x-2\">\n                <ArrowLeft className=\"w-4 h-4\" />\n                <span>返回关于我们</span>\n              </Link>\n            </Button>\n          </div>\n\n          <Card className=\"mb-8\">\n            <CardHeader className=\"text-center\">\n              <div className=\"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                <BookOpen className=\"w-8 h-8 text-white\" />\n              </div>\n              <CardTitle className=\"text-3xl\">项目白皮书</CardTitle>\n              <CardDescription className=\"text-lg\">\n                YYZ抑郁症研究与帮助平台：数字化心理健康服务的创新实践\n              </CardDescription>\n            </CardHeader>\n          </Card>\n        </motion.div>\n\n        {/* Executive Summary */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"space-y-8\"\n        >\n          <Card>\n            <CardHeader>\n              <CardTitle>执行摘要</CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <p className=\"text-gray-700 leading-relaxed\">\n                YYZ项目是一个创新的数字化心理健康服务平台，专注于为抑郁症患者及高风险人群提供\n                全方位的支持服务。通过整合人工智能、专业心理学知识和社区力量，我们致力于降低\n                心理健康服务的门槛，提高服务的可及性和有效性。\n              </p>\n              <p className=\"text-gray-700 leading-relaxed\">\n                本白皮书详细阐述了项目的社会价值、技术创新、商业模式和发展规划，为投资者、\n                合作伙伴和社会各界了解项目提供全面的参考。\n              </p>\n            </CardContent>\n          </Card>\n\n          {/* Market Analysis */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <BarChart3 className=\"w-5 h-5\" />\n                <span>市场分析</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"space-y-4\">\n                  <h4 className=\"font-semibold text-gray-900\">市场规模</h4>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex justify-between items-center p-3 bg-blue-50 rounded-lg\">\n                      <span className=\"text-sm font-medium\">全球抑郁症患者</span>\n                      <span className=\"text-blue-600 font-bold\">3.5亿+</span>\n                    </div>\n                    <div className=\"flex justify-between items-center p-3 bg-purple-50 rounded-lg\">\n                      <span className=\"text-sm font-medium\">中国患病率</span>\n                      <span className=\"text-purple-600 font-bold\">4.2%</span>\n                    </div>\n                    <div className=\"flex justify-between items-center p-3 bg-green-50 rounded-lg\">\n                      <span className=\"text-sm font-medium\">就医率</span>\n                      <span className=\"text-green-600 font-bold\">仅10%</span>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"space-y-4\">\n                  <h4 className=\"font-semibold text-gray-900\">市场痛点</h4>\n                  <ul className=\"text-gray-700 space-y-2 text-sm\">\n                    <li>• 专业心理咨询师资源严重不足</li>\n                    <li>• 传统服务成本高昂，普及率低</li>\n                    <li>• 社会认知不足，存在偏见和歧视</li>\n                    <li>• 缺乏有效的早期筛查和干预机制</li>\n                    <li>• 数字化服务质量参差不齐</li>\n                  </ul>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Innovation Points */}\n          <Card>\n            <CardHeader>\n              <CardTitle>创新亮点</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"p-4 border border-blue-200 rounded-lg\">\n                  <h4 className=\"font-semibold text-blue-900 mb-2\">🤖 AI技术创新</h4>\n                  <p className=\"text-blue-700 text-sm\">\n                    基于大语言模型的智能陪伴系统，提供个性化的情感支持和专业建议。\n                  </p>\n                </div>\n                <div className=\"p-4 border border-purple-200 rounded-lg\">\n                  <h4 className=\"font-semibold text-purple-900 mb-2\">📊 数据驱动</h4>\n                  <p className=\"text-purple-700 text-sm\">\n                    通过情绪追踪和行为分析，为用户提供科学的心理健康洞察。\n                  </p>\n                </div>\n                <div className=\"p-4 border border-green-200 rounded-lg\">\n                  <h4 className=\"font-semibold text-green-900 mb-2\">🏥 医疗整合</h4>\n                  <p className=\"text-green-700 text-sm\">\n                    与传统医疗体系无缝对接，为线下就医提供数据支持。\n                  </p>\n                </div>\n                <div className=\"p-4 border border-orange-200 rounded-lg\">\n                  <h4 className=\"font-semibold text-orange-900 mb-2\">🌐 社区生态</h4>\n                  <p className=\"text-orange-700 text-sm\">\n                    构建专业志愿者网络，形成可持续的社区支持体系。\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Social Impact */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Users className=\"w-5 h-5\" />\n                <span>社会价值</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"text-center p-4\">\n                    <div className=\"w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-2xl font-bold\">1M+</span>\n                    </div>\n                    <h4 className=\"font-semibold mb-2\">预期服务用户</h4>\n                    <p className=\"text-gray-600 text-sm\">3年内服务超过100万用户</p>\n                  </div>\n                  <div className=\"text-center p-4\">\n                    <div className=\"w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-2xl font-bold\">24/7</span>\n                    </div>\n                    <h4 className=\"font-semibold mb-2\">全天候服务</h4>\n                    <p className=\"text-gray-600 text-sm\">打破时间和地域限制</p>\n                  </div>\n                  <div className=\"text-center p-4\">\n                    <div className=\"w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-2xl font-bold\">0¥</span>\n                    </div>\n                    <h4 className=\"font-semibold mb-2\">完全免费</h4>\n                    <p className=\"text-gray-600 text-sm\">降低心理健康服务门槛</p>\n                  </div>\n                </div>\n                \n                <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg\">\n                  <h4 className=\"font-semibold mb-3\">预期社会效益</h4>\n                  <ul className=\"text-gray-700 space-y-2 text-sm\">\n                    <li>• <strong>提高早期识别率</strong>：通过便捷的评估工具，帮助更多人及早发现心理健康问题</li>\n                    <li>• <strong>减少社会成本</strong>：预防性干预降低重度抑郁症的治疗成本</li>\n                    <li>• <strong>促进社会认知</strong>：通过科普教育消除对心理疾病的偏见</li>\n                    <li>• <strong>培养专业人才</strong>：为心理健康领域培养更多志愿者和专业人士</li>\n                  </ul>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Privacy & Security */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Shield className=\"w-5 h-5\" />\n                <span>隐私与安全</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h4 className=\"font-semibold mb-3\">数据保护措施</h4>\n                  <ul className=\"text-gray-700 space-y-2 text-sm\">\n                    <li>• 端到端加密传输</li>\n                    <li>• 本地数据存储优先</li>\n                    <li>• 匿名化数据处理</li>\n                    <li>• 定期安全审计</li>\n                    <li>• 符合GDPR等国际标准</li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold mb-3\">用户权利保障</h4>\n                  <ul className=\"text-gray-700 space-y-2 text-sm\">\n                    <li>• 完全匿名使用权</li>\n                    <li>• 数据删除权</li>\n                    <li>• 服务退出权</li>\n                    <li>• 透明的隐私政策</li>\n                    <li>• 用户数据控制权</li>\n                  </ul>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Development Roadmap */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Globe className=\"w-5 h-5\" />\n                <span>发展规划</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div className=\"space-y-4\">\n                    <h4 className=\"font-semibold text-gray-900\">短期目标（6-12个月）</h4>\n                    <ul className=\"text-gray-700 space-y-2 text-sm\">\n                      <li>• 完成MVP开发和测试</li>\n                      <li>• 招募100名专业志愿者</li>\n                      <li>• 服务1万名用户</li>\n                      <li>• 建立合作伙伴网络</li>\n                    </ul>\n                  </div>\n                  <div className=\"space-y-4\">\n                    <h4 className=\"font-semibold text-gray-900\">中期目标（1-3年）</h4>\n                    <ul className=\"text-gray-700 space-y-2 text-sm\">\n                      <li>• 用户规模达到100万</li>\n                      <li>• 推出移动应用</li>\n                      <li>• 与医疗机构深度合作</li>\n                      <li>• 开展学术研究合作</li>\n                    </ul>\n                  </div>\n                </div>\n                \n                <div className=\"bg-gray-50 p-6 rounded-lg\">\n                  <h4 className=\"font-semibold mb-3\">长期愿景（3-5年）</h4>\n                  <p className=\"text-gray-700 text-sm leading-relaxed\">\n                    成为中国领先的数字化心理健康服务平台，建立覆盖全国的志愿者网络，\n                    与政府、医疗机构、高校等建立深度合作关系，推动心理健康服务的\n                    标准化和普及化，为构建健康中国贡献力量。\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Call to Action */}\n          <Card className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n            <CardContent className=\"pt-6\">\n              <div className=\"text-center\">\n                <h3 className=\"text-xl font-bold mb-4\">携手共建心理健康生态</h3>\n                <p className=\"mb-6 text-blue-100\">\n                  我们诚邀各界人士加入这一有意义的事业，共同为心理健康服务的创新发展贡献力量。\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                  <Button size=\"lg\" variant=\"secondary\" asChild>\n                    <Link href=\"/join\">加入我们</Link>\n                  </Button>\n                  <Button size=\"lg\" variant=\"outline\" className=\"text-white border-white hover:bg-white hover:text-blue-600\" asChild>\n                    <Link href=\"/docs/project-vision\">查看项目初衷</Link>\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,OAAO;gCAAC,WAAU;0CACxC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;;sDAC5B,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;kDAChC,8OAAC,gIAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;8BAQ3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;sDAK7C,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAQjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,8OAAC;wEAAK,WAAU;kFAA0B;;;;;;;;;;;;0EAE5C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;0EAE9C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,8OAAC;wEAAK,WAAU;kFAA2B;;;;;;;;;;;;;;;;;;;;;;;;0DAIjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAIvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAA0B;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS/C,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAgC;;;;;;;;;;;0EAElD,8OAAC;gEAAG,WAAU;0EAAqB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAgC;;;;;;;;;;;0EAElD,8OAAC;gEAAG,WAAU;0EAAqB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAgC;;;;;;;;;;;0EAElD,8OAAC;gEAAG,WAAU;0EAAqB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;;oEAAG;kFAAE,8OAAC;kFAAO;;;;;;oEAAgB;;;;;;;0EAC9B,8OAAC;;oEAAG;kFAAE,8OAAC;kFAAO;;;;;;oEAAe;;;;;;;0EAC7B,8OAAC;;oEAAG;kFAAE,8OAAC;kFAAO;;;;;;oEAAe;;;;;;;0EAC7B,8OAAC;;oEAAG;kFAAE,8OAAC;kFAAO;;;;;;oEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvC,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;;;;;;;;;;;;;kEAGR,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;0DAKV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAW7D,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAY,OAAO;8DAC3C,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAQ;;;;;;;;;;;8DAErB,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAU,WAAU;oDAA6D,OAAO;8DAChH,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD", "debugId": null}}]}