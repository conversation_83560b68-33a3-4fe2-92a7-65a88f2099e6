{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%8A%91%E9%83%81%E7%97%87%E7%A0%94%E7%A9%B6%E4%B8%8E%E5%B8%AE%E5%8A%A9/yyz-depression-help/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%8A%91%E9%83%81%E7%97%87%E7%A0%94%E7%A9%B6%E4%B8%8E%E5%B8%AE%E5%8A%A9/yyz-depression-help/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { MessageCircle, Send, Bot, User, Heart, Sparkles } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\n\ninterface Message {\n  id: string\n  content: string\n  sender: 'user' | 'ai'\n  timestamp: Date\n}\n\nconst AI_RESPONSES = [\n  \"我听到了你的话，感谢你愿意和我分享。你现在的感受是完全可以理解的。\",\n  \"每个人都有低落的时候，这并不意味着你有什么问题。你愿意告诉我更多吗？\",\n  \"你的感受很重要，我想更好地理解你现在的状况。\",\n  \"听起来你正在经历一些困难。记住，寻求帮助是勇敢的表现。\",\n  \"我能感受到你的痛苦。虽然我是AI，但我希望能给你一些温暖和支持。\",\n  \"你不是一个人在面对这些。有很多人关心你，包括专业的帮助者。\",\n  \"每一天都是新的开始。即使现在很难，但请相信事情会慢慢好起来的。\",\n  \"你已经很勇敢了，能够表达自己的感受需要很大的勇气。\"\n]\n\nconst MOOD_SUGGESTIONS = [\n  \"我感到很沮丧\",\n  \"今天过得不太好\",\n  \"我觉得很孤独\",\n  \"我想找人聊聊\",\n  \"我需要一些鼓励\",\n  \"我感到焦虑不安\"\n]\n\nexport default function ChatPage() {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      content: '你好！我是你的AI陪伴者小光。我在这里倾听你的心声，陪伴你度过困难时光。请随时告诉我你的感受，我会尽我所能给你支持和理解。',\n      sender: 'ai',\n      timestamp: new Date()\n    }\n  ])\n  const [inputValue, setInputValue] = useState('')\n  const [isTyping, setIsTyping] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  const sendMessage = async (content: string) => {\n    if (!content.trim()) return\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      content: content.trim(),\n      sender: 'user',\n      timestamp: new Date()\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInputValue('')\n    setIsTyping(true)\n\n    // Simulate AI response delay\n    setTimeout(() => {\n      const aiResponse: Message = {\n        id: (Date.now() + 1).toString(),\n        content: AI_RESPONSES[Math.floor(Math.random() * AI_RESPONSES.length)],\n        sender: 'ai',\n        timestamp: new Date()\n      }\n      setMessages(prev => [...prev, aiResponse])\n      setIsTyping(false)\n    }, 1000 + Math.random() * 2000)\n  }\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    sendMessage(inputValue)\n  }\n\n  const handleSuggestionClick = (suggestion: string) => {\n    sendMessage(suggestion)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50\">\n      <div className=\"mx-auto max-w-4xl px-6 py-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <Card className=\"mb-6\">\n            <CardHeader className=\"text-center\">\n              <div className=\"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center\">\n                <MessageCircle className=\"w-8 h-8 text-white\" />\n              </div>\n              <CardTitle className=\"text-2xl\">AI陪聊</CardTitle>\n              <CardDescription>\n                24小时温暖陪伴，倾听你的心声\n              </CardDescription>\n            </CardHeader>\n          </Card>\n        </motion.div>\n\n        {/* Chat Container */}\n        <Card className=\"h-[600px] flex flex-col\">\n          {/* Messages */}\n          <CardContent className=\"flex-1 overflow-y-auto p-6 space-y-4\">\n            {messages.map((message) => (\n              <motion.div\n                key={message.id}\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3 }}\n                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div className={`flex items-start space-x-3 max-w-[80%] ${\n                  message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n                }`}>\n                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                    message.sender === 'user' \n                      ? 'bg-blue-500' \n                      : 'bg-gradient-to-br from-purple-500 to-pink-600'\n                  }`}>\n                    {message.sender === 'user' ? (\n                      <User className=\"w-4 h-4 text-white\" />\n                    ) : (\n                      <Bot className=\"w-4 h-4 text-white\" />\n                    )}\n                  </div>\n                  <div className={`rounded-lg p-3 ${\n                    message.sender === 'user'\n                      ? 'bg-blue-500 text-white'\n                      : 'bg-gray-100 text-gray-900'\n                  }`}>\n                    <p className=\"text-sm\">{message.content}</p>\n                    <p className={`text-xs mt-1 ${\n                      message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'\n                    }`}>\n                      {message.timestamp.toLocaleTimeString('zh-CN', { \n                        hour: '2-digit', \n                        minute: '2-digit' \n                      })}\n                    </p>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n            \n            {isTyping && (\n              <motion.div\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"flex justify-start\"\n              >\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center\">\n                    <Bot className=\"w-4 h-4 text-white\" />\n                  </div>\n                  <div className=\"bg-gray-100 rounded-lg p-3\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            )}\n            <div ref={messagesEndRef} />\n          </CardContent>\n\n          {/* Quick Suggestions */}\n          {messages.length === 1 && (\n            <div className=\"px-6 py-4 border-t border-gray-200\">\n              <p className=\"text-sm text-gray-600 mb-3\">快速开始对话：</p>\n              <div className=\"flex flex-wrap gap-2\">\n                {MOOD_SUGGESTIONS.map((suggestion, index) => (\n                  <Button\n                    key={index}\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => handleSuggestionClick(suggestion)}\n                    className=\"text-xs\"\n                  >\n                    {suggestion}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Input */}\n          <div className=\"p-6 border-t border-gray-200\">\n            <form onSubmit={handleSubmit} className=\"flex space-x-3\">\n              <input\n                type=\"text\"\n                value={inputValue}\n                onChange={(e) => setInputValue(e.target.value)}\n                placeholder=\"输入你想说的话...\"\n                className=\"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                disabled={isTyping}\n              />\n              <Button \n                type=\"submit\" \n                disabled={!inputValue.trim() || isTyping}\n                className=\"bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700\"\n              >\n                <Send className=\"w-4 h-4\" />\n              </Button>\n            </form>\n            <p className=\"text-xs text-gray-500 mt-2 text-center\">\n              <Heart className=\"w-3 h-3 inline mr-1\" />\n              AI陪聊仅供情感支持，不能替代专业心理咨询\n            </p>\n          </div>\n        </Card>\n\n        {/* Disclaimer */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.5 }}\n          className=\"mt-6\"\n        >\n          <Card className=\"bg-yellow-50 border-yellow-200\">\n            <CardContent className=\"pt-6\">\n              <div className=\"flex items-start space-x-3\">\n                <Sparkles className=\"w-5 h-5 text-yellow-600 mt-0.5\" />\n                <div>\n                  <h4 className=\"font-semibold text-yellow-800 mb-1\">温馨提示</h4>\n                  <p className=\"text-sm text-yellow-700\">\n                    我是AI陪伴者，可以倾听和陪伴，但无法提供专业的心理治疗。如果您需要专业帮助，请联系心理咨询师或医生。\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAeA,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS,QAAQ,IAAI;YACrB,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,YAAY;QAEZ,6BAA6B;QAC7B,WAAW;YACT,MAAM,aAAsB;gBAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;gBACtE,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YACzC,YAAY;QACd,GAAG,OAAO,KAAK,MAAM,KAAK;IAC5B;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,wBAAwB,CAAC;QAC7B,YAAY;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAW;;;;;;8CAChC,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;;;;;;;;;;;8BAQvB,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCAEd,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;gCACpB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,KAAK,SAAS,gBAAgB,iBAAiB;kDAEhF,cAAA,8OAAC;4CAAI,WAAW,CAAC,uCAAuC,EACtD,QAAQ,MAAM,KAAK,SAAS,qCAAqC,IACjE;;8DACA,8OAAC;oDAAI,WAAW,CAAC,sDAAsD,EACrE,QAAQ,MAAM,KAAK,SACf,gBACA,iDACJ;8DACC,QAAQ,MAAM,KAAK,uBAClB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;6EAEhB,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAGnB,8OAAC;oDAAI,WAAW,CAAC,eAAe,EAC9B,QAAQ,MAAM,KAAK,SACf,2BACA,6BACJ;;sEACA,8OAAC;4DAAE,WAAU;sEAAW,QAAQ,OAAO;;;;;;sEACvC,8OAAC;4DAAE,WAAW,CAAC,aAAa,EAC1B,QAAQ,MAAM,KAAK,SAAS,kBAAkB,iBAC9C;sEACC,QAAQ,SAAS,CAAC,kBAAkB,CAAC,SAAS;gEAC7C,MAAM;gEACN,QAAQ;4DACV;;;;;;;;;;;;;;;;;;uCAhCD,QAAQ,EAAE;;;;;gCAuClB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;4DAAkD,OAAO;gEAAE,gBAAgB;4DAAO;;;;;;sEACjG,8OAAC;4DAAI,WAAU;4DAAkD,OAAO;gEAAE,gBAAgB;4DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM3G,8OAAC;oCAAI,KAAK;;;;;;;;;;;;wBAIX,SAAS,MAAM,KAAK,mBACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,YAAY,sBACjC,8OAAC,kIAAA,CAAA,SAAM;4CAEL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,sBAAsB;4CACrC,WAAU;sDAET;2CANI;;;;;;;;;;;;;;;;sCAcf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aAAY;4CACZ,WAAU;4CACV,UAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,CAAC,WAAW,IAAI,MAAM;4CAChC,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGpB,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;;;;;;;;;;;;;8BAO/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;8BAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD", "debugId": null}}]}