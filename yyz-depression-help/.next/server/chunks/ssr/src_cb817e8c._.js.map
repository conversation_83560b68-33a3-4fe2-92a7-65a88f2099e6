{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%8A%91%E9%83%81%E7%97%87%E7%A0%94%E7%A9%B6%E4%B8%8E%E5%B8%AE%E5%8A%A9/yyz-depression-help/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E6%8A%91%E9%83%81%E7%97%87%E7%A0%94%E7%A9%B6%E4%B8%8E%E5%B8%AE%E5%8A%A9/yyz-depression-help/src/app/about/page.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Heart, Target, Users, Lightbulb, FileText, Map, Calendar, ArrowRight } from 'lucide-react'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\n\nconst milestones = [\n  {\n    phase: '阶段一',\n    time: '0~3个月',\n    title: 'Web Demo开发',\n    description: '项目介绍站点+AI陪聊+评估工具原型',\n    status: 'completed'\n  },\n  {\n    phase: '阶段二',\n    time: '3~6个月',\n    title: '用户测试与团队建设',\n    description: '完善功能模块，建立专业志愿团队和专家顾问组',\n    status: 'current'\n  },\n  {\n    phase: '阶段三',\n    time: '6~12个月',\n    title: 'App原型开发',\n    description: '构建App原型，优化数据结构，跨平台同步',\n    status: 'planned'\n  },\n  {\n    phase: '阶段四',\n    time: '1~2年',\n    title: '全国支持网络',\n    description: '建立全国范围内支持网络，推动与政策协同与高校试点',\n    status: 'planned'\n  }\n]\n\nconst values = [\n  {\n    icon: Target,\n    title: '早识别',\n    description: '使用评估工具、AI陪聊、情绪日志，帮助用户早发现早介入'\n  },\n  {\n    icon: Heart,\n    title: '易使用',\n    description: '平台界面简洁温和、可匿名、无登录体验核心功能'\n  },\n  {\n    icon: Users,\n    title: '多维度',\n    description: '兼顾医学、心理、社会工作视角，构建支持生态'\n  },\n  {\n    icon: Lightbulb,\n    title: '真共情',\n    description: '数字人陪伴+人类志愿者支援，打造可信赖的温度'\n  }\n]\n\nexport default function AboutPage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50\">\n      <div className=\"mx-auto max-w-6xl px-6 py-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <Card className=\"mb-8\">\n            <CardHeader className=\"text-center\">\n              <div className=\"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                <Heart className=\"w-8 h-8 text-white\" />\n              </div>\n              <CardTitle className=\"text-3xl\">关于我们</CardTitle>\n              <CardDescription className=\"text-lg\">\n                了解YYZ项目的初衷、愿景与发展规划\n              </CardDescription>\n            </CardHeader>\n          </Card>\n        </motion.div>\n\n        {/* Mission Statement */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"mb-8\"\n        >\n          <Card className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n            <CardContent className=\"pt-6\">\n              <div className=\"text-center\">\n                <h2 className=\"text-2xl font-bold mb-4\">项目愿景</h2>\n                <p className=\"text-lg leading-relaxed max-w-4xl mx-auto mb-6\">\n                  构建一个以人为本、科技赋能的数字心理健康生态，帮助抑郁症患者\"认识自己、倾诉情绪、获得治疗、重回生活\"。\n                </p>\n                <blockquote className=\"text-xl italic border-l-4 border-white pl-4 text-blue-100\">\n                  \"愿所有被困在夜里的灵魂，都能看到清晨的第一缕光。\"\n                </blockquote>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        {/* Core Values */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6 text-center\">核心价值主张</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {values.map((value, index) => {\n              const Icon = value.icon\n              return (\n                <motion.div\n                  key={value.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.1 * index }}\n                >\n                  <Card className=\"h-full text-center hover:shadow-lg transition-shadow\">\n                    <CardHeader>\n                      <div className=\"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                        <Icon className=\"w-6 h-6 text-white\" />\n                      </div>\n                      <CardTitle className=\"text-lg\">{value.title}</CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <p className=\"text-gray-600 text-sm\">{value.description}</p>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )\n            })}\n          </div>\n        </motion.div>\n\n        {/* Roadmap */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          className=\"mb-8\"\n        >\n          <Card>\n            <CardHeader className=\"text-center\">\n              <div className=\"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center\">\n                <Map className=\"w-6 h-6 text-white\" />\n              </div>\n              <CardTitle className=\"text-2xl\">发展路线图</CardTitle>\n              <CardDescription>\n                阶段性开发计划与里程碑\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-6\">\n                {milestones.map((milestone, index) => (\n                  <motion.div\n                    key={milestone.phase}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.6, delay: 0.1 * index }}\n                    className=\"flex items-start space-x-4\"\n                  >\n                    <div className={`w-4 h-4 rounded-full mt-2 flex-shrink-0 ${\n                      milestone.status === 'completed' ? 'bg-green-500' :\n                      milestone.status === 'current' ? 'bg-blue-500' :\n                      'bg-gray-300'\n                    }`}></div>\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-2 mb-2\">\n                        <h3 className=\"font-semibold text-gray-900\">{milestone.phase}</h3>\n                        <span className=\"text-sm text-gray-500\">({milestone.time})</span>\n                        {milestone.status === 'current' && (\n                          <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                            进行中\n                          </span>\n                        )}\n                        {milestone.status === 'completed' && (\n                          <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\n                            已完成\n                          </span>\n                        )}\n                      </div>\n                      <h4 className=\"font-medium text-gray-800 mb-1\">{milestone.title}</h4>\n                      <p className=\"text-gray-600 text-sm\">{milestone.description}</p>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        {/* Quick Links */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"mb-8\"\n        >\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6 text-center\">了解更多</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <Card className=\"hover:shadow-lg transition-shadow cursor-pointer group\">\n              <CardHeader>\n                <div className=\"flex items-center space-x-3\">\n                  <FileText className=\"w-8 h-8 text-blue-600\" />\n                  <div>\n                    <CardTitle className=\"group-hover:text-blue-600 transition-colors\">\n                      项目初衷\n                    </CardTitle>\n                    <CardDescription>\n                      查看完整的产品需求与开发文档\n                    </CardDescription>\n                  </div>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-gray-600 mb-4\">\n                  深入了解YYZ项目的技术架构、功能模块设计和开发计划。\n                </p>\n                <Button variant=\"outline\" size=\"sm\" className=\"group-hover:border-blue-500 group-hover:text-blue-600\">\n                  查看文档 <ArrowRight className=\"w-3 h-3 ml-1\" />\n                </Button>\n              </CardContent>\n            </Card>\n\n            <Card className=\"hover:shadow-lg transition-shadow cursor-pointer group\">\n              <CardHeader>\n                <div className=\"flex items-center space-x-3\">\n                  <Calendar className=\"w-8 h-8 text-purple-600\" />\n                  <div>\n                    <CardTitle className=\"group-hover:text-purple-600 transition-colors\">\n                      白皮书\n                    </CardTitle>\n                    <CardDescription>\n                      项目背景、愿景与社会价值\n                    </CardDescription>\n                  </div>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-gray-600 mb-4\">\n                  了解项目的社会背景、核心价值主张和未来发展愿景。\n                </p>\n                <Button variant=\"outline\" size=\"sm\" className=\"group-hover:border-purple-500 group-hover:text-purple-600\">\n                  查看白皮书 <ArrowRight className=\"w-3 h-3 ml-1\" />\n                </Button>\n              </CardContent>\n            </Card>\n          </div>\n        </motion.div>\n\n        {/* CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 1.0 }}\n        >\n          <Card className=\"bg-gradient-to-r from-orange-500 to-pink-600 text-white\">\n            <CardContent className=\"pt-6\">\n              <div className=\"text-center\">\n                <h2 className=\"text-2xl font-bold mb-4\">加入我们的使命</h2>\n                <p className=\"text-lg mb-6 text-orange-100\">\n                  如果你也相信科技可以传递温暖，愿意为心理健康事业贡献力量\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                  <Button size=\"lg\" variant=\"secondary\" asChild>\n                    <Link href=\"/join\" className=\"flex items-center space-x-2\">\n                      <Users className=\"w-5 h-5\" />\n                      <span>成为志愿者</span>\n                    </Link>\n                  </Button>\n                  <Button size=\"lg\" variant=\"outline\" className=\"text-white border-white hover:bg-white hover:text-orange-600\" asChild>\n                    <Link href=\"/assessment\">开始体验</Link>\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;IACV;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;IACV;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;IACV;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;IACV;CACD;AAED,MAAM,SAAS;IACb;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,4MAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAW;;;;;;8CAChC,8OAAC,gIAAA,CAAA,kBAAe;oCAAC,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;8BAQ3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAAiD;;;;;;kDAG9D,8OAAC;wCAAW,WAAU;kDAA4D;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS1F,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAClE,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO;gCAClB,MAAM,OAAO,MAAM,IAAI;gCACvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAM;8CAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAW,MAAM,KAAK;;;;;;;;;;;;0DAE7C,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;8DAAyB,MAAM,WAAW;;;;;;;;;;;;;;;;;mCAbtD,MAAM,KAAK;;;;;4BAkBtB;;;;;;;;;;;;8BAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;kDAChC,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM;4CAAM;4CAChD,WAAU;;8DAEV,8OAAC;oDAAI,WAAW,CAAC,wCAAwC,EACvD,UAAU,MAAM,KAAK,cAAc,iBACnC,UAAU,MAAM,KAAK,YAAY,gBACjC,eACA;;;;;;8DACF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA+B,UAAU,KAAK;;;;;;8EAC5D,8OAAC;oEAAK,WAAU;;wEAAwB;wEAAE,UAAU,IAAI;wEAAC;;;;;;;gEACxD,UAAU,MAAM,KAAK,2BACpB,8OAAC;oEAAK,WAAU;8EAA2D;;;;;;gEAI5E,UAAU,MAAM,KAAK,6BACpB,8OAAC;oEAAK,WAAU;8EAA6D;;;;;;;;;;;;sEAKjF,8OAAC;4DAAG,WAAU;sEAAkC,UAAU,KAAK;;;;;;sEAC/D,8OAAC;4DAAE,WAAU;sEAAyB,UAAU,WAAW;;;;;;;;;;;;;2CA3BxD,UAAU,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;8BAqChC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAClE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;;0EACC,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAA8C;;;;;;0EAGnE,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;;;;;;;;;;;;sDAMvB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;oDAAK,WAAU;;wDAAwD;sEAC/F,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAKjC,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;;0EACC,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAgD;;;;;;0EAGrE,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;;;;;;;;;;;;sDAMvB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;oDAAK,WAAU;;wDAA4D;sEAClG,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAA+B;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAY,OAAO;0DAC3C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,WAAU;gDAA+D,OAAO;0DAClH,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7C", "debugId": null}}]}