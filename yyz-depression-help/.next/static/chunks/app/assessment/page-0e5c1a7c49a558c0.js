(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[197],{18475:(e,r,t)=>{Promise.resolve().then(t.bind(t,89254))},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var l=t(95155),a=t(12115),n=t(74466),i=t(59434);let s=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",warm:"bg-gradient-to-r from-orange-400 to-pink-400 text-white hover:from-orange-500 hover:to-pink-500",calm:"bg-gradient-to-r from-blue-400 to-purple-400 text-white hover:from-blue-500 hover:to-purple-500"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,r)=>{let{className:t,variant:a,size:n,...o}=e;return(0,l.jsx)("button",{className:(0,i.cn)(s({variant:a,size:n,className:t})),ref:r,...o})});o.displayName="Button"},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40646:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},49376:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},59434:(e,r,t)=>{"use strict";t.d(r,{$C:()=>u,$Z:()=>i,C1:()=>s,IG:()=>m,Yq:()=>c,_o:()=>d,aX:()=>o,cn:()=>n});var l=t(52596),a=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,l.$)(r))}let i=[{id:1,question:"做事时提不起劲或没有兴趣",description:"在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"},{id:2,question:"感到心情低落、沮丧或绝望",description:"在过去两周内，您有多少天感到心情低落、沮丧或绝望？"},{id:3,question:"入睡困难、睡不安稳或睡眠过多",description:"在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"},{id:4,question:"感觉疲倦或没有活力",description:"在过去两周内，您有多少天感觉疲倦或没有活力？"},{id:5,question:"食欲不振或吃太多",description:"在过去两周内，您有多少天有食欲不振或吃太多的问题？"},{id:6,question:"觉得自己很糟糕或觉得自己很失败",description:"在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"},{id:7,question:"对事物专注有困难",description:"在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"},{id:8,question:"动作或说话速度缓慢或烦躁不安",description:"在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"},{id:9,question:"有不如死掉或伤害自己的念头",description:"在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"}],s=[{value:0,label:"完全没有",days:"0天"},{value:1,label:"几天",days:"1-6天"},{value:2,label:"一半以上的天数",days:"7-11天"},{value:3,label:"几乎每天",days:"12-14天"}];function o(e){return e<=4?{level:"minimal",title:"轻微或无抑郁症状",description:"您目前的症状较轻微，建议继续保持良好的生活习惯。",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"}:e<=9?{level:"mild",title:"轻度抑郁症状",description:"您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}:e<=14?{level:"moderate",title:"中度抑郁症状",description:"您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"}:e<=19?{level:"moderately-severe",title:"中重度抑郁症状",description:"您的症状较为严重，强烈建议寻求专业的心理健康服务。",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}:{level:"severe",title:"重度抑郁症状",description:"您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",color:"text-red-800",bgColor:"bg-red-100",borderColor:"border-red-300"}}let d=[{id:"very-happy",label:"非常开心",emoji:"\uD83D\uDE04",color:"#10b981"},{id:"happy",label:"开心",emoji:"\uD83D\uDE0A",color:"#34d399"},{id:"neutral",label:"平静",emoji:"\uD83D\uDE10",color:"#6b7280"},{id:"sad",label:"难过",emoji:"\uD83D\uDE22",color:"#f59e0b"},{id:"very-sad",label:"非常难过",emoji:"\uD83D\uDE2D",color:"#ef4444"},{id:"anxious",label:"焦虑",emoji:"\uD83D\uDE30",color:"#8b5cf6"},{id:"angry",label:"愤怒",emoji:"\uD83D\uDE20",color:"#dc2626"},{id:"tired",label:"疲惫",emoji:"\uD83D\uDE34",color:"#64748b"}];function c(e){return e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}function u(){return Math.random().toString(36).substr(2,9)}let m={get:e=>{try{let r=localStorage.getItem(e);return r?JSON.parse(r):null}catch(e){return null}},set:(e,r)=>{try{localStorage.setItem(e,JSON.stringify(r))}catch(e){}},remove:e=>{try{localStorage.removeItem(e)}catch(e){}}}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>s});var l=t(95155),a=t(12115),n=t(59434);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let s=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});s.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},89254:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>P});var l=t(95155),a=t(12115),n=t(51934),i=t(40646),s=t(49376),o=t(35169),d=t(30285),c=t(66695);function u(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}t(47650);var m=Symbol("radix.slottable");function p(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===m}var x=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=function(e){let r=function(e){let r=a.forwardRef((e,r)=>{let{children:t,...l}=e;if(a.isValidElement(t)){var n;let e,i,s=(n=t,(i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(i=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),o=function(e,r){let t={...r};for(let l in r){let a=e[l],n=r[l];/^on[A-Z]/.test(l)?a&&n?t[l]=(...e)=>{let r=n(...e);return a(...e),r}:a&&(t[l]=a):"style"===l?t[l]={...a,...n}:"className"===l&&(t[l]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(l,t.props);return t.type!==a.Fragment&&(o.ref=r?function(...e){return r=>{let t=!1,l=e.map(e=>{let l=u(e,r);return t||"function"!=typeof l||(t=!0),l});if(t)return()=>{for(let r=0;r<l.length;r++){let t=l[r];"function"==typeof t?t():u(e[r],null)}}}}(r,s):s),a.cloneElement(t,o)}return a.Children.count(t)>1?a.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=a.forwardRef((e,t)=>{let{children:n,...i}=e,s=a.Children.toArray(n),o=s.find(p);if(o){let e=o.props.children,n=s.map(r=>r!==o?r:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,l.jsx)(r,{...i,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,l.jsx)(r,{...i,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}(`Primitive.${r}`),n=a.forwardRef((e,a)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(n?t:r,{...i,ref:a})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{}),f="Progress",[h,b]=function(e,r=[]){let t=[],n=()=>{let r=t.map(e=>a.createContext(e));return function(t){let l=t?.[e]||r;return a.useMemo(()=>({[`__scope${e}`]:{...t,[e]:l}}),[t,l])}};return n.scopeName=e,[function(r,n){let i=a.createContext(n),s=t.length;t=[...t,n];let o=r=>{let{scope:t,children:n,...o}=r,d=t?.[e]?.[s]||i,c=a.useMemo(()=>o,Object.values(o));return(0,l.jsx)(d.Provider,{value:c,children:n})};return o.displayName=r+"Provider",[o,function(t,l){let o=l?.[e]?.[s]||i,d=a.useContext(o);if(d)return d;if(void 0!==n)return n;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=t.reduce((r,{useScope:t,scopeName:l})=>{let a=t(e)[`__scope${l}`];return{...r,...a}},{});return a.useMemo(()=>({[`__scope${r.scopeName}`]:l}),[l])}};return t.scopeName=r.scopeName,t}(n,...r)]}(f),[v,g]=h(f),y=a.forwardRef((e,r)=>{var t,a,n,i;let{__scopeProgress:s,value:o=null,max:d,getValueLabel:c=w,...u}=e;(d||0===d)&&!Z(d)&&console.error((t="".concat(d),a="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(a,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let m=Z(d)?d:100;null===o||$(o,m)||console.error((n="".concat(o),i="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let p=$(o,m)?o:null,f=k(p)?c(p,m):void 0;return(0,l.jsx)(v,{scope:s,value:p,max:m,children:(0,l.jsx)(x.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":k(p)?p:void 0,"aria-valuetext":f,role:"progressbar","data-state":C(p,m),"data-value":null!=p?p:void 0,"data-max":m,...u,ref:r})})});y.displayName=f;var j="ProgressIndicator",N=a.forwardRef((e,r)=>{var t;let{__scopeProgress:a,...n}=e,i=g(j,a);return(0,l.jsx)(x.div,{"data-state":C(i.value,i.max),"data-value":null!=(t=i.value)?t:void 0,"data-max":i.max,...n,ref:r})});function w(e,r){return"".concat(Math.round(e/r*100),"%")}function C(e,r){return null==e?"indeterminate":e===r?"complete":"loading"}function k(e){return"number"==typeof e}function Z(e){return k(e)&&!isNaN(e)&&e>0}function $(e,r){return k(e)&&!isNaN(e)&&e<=r&&e>=0}N.displayName=j;var R=t(59434);let A=a.forwardRef((e,r)=>{let{className:t,value:a,...n}=e;return(0,l.jsx)(y,{ref:r,className:(0,R.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...n,children:(0,l.jsx)(N,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})});function P(){let[e,r]=(0,a.useState)(0),[t,u]=(0,a.useState)(Array(R.$Z.length).fill(-1)),[,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1),f=(e+1)/R.$Z.length*100,h=t.reduce((e,r)=>e+(r>-1?r:0),0),b=(0,R.aX)(h),v=l=>{let a=[...t];a[e]=l,u(a),e<R.$Z.length-1?setTimeout(()=>{r(e+1)},300):(m(!0),setTimeout(()=>{x(!0)},500))};return p?(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-12",children:(0,l.jsx)("div",{className:"mx-auto max-w-4xl px-6",children:(0,l.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,l.jsx)(c.Zp,{className:"mb-8",children:(0,l.jsxs)(c.aR,{className:"text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,l.jsx)(c.ZB,{className:"text-2xl",children:"评估完成"}),(0,l.jsx)(c.BT,{children:"感谢您完成PHQ-9抑郁症评估量表"})]})}),(0,l.jsxs)(c.Zp,{className:"mb-8 border-2 ".concat(b.borderColor),children:[(0,l.jsxs)(c.aR,{className:"".concat(b.bgColor," rounded-t-lg"),children:[(0,l.jsxs)(c.ZB,{className:"text-xl ".concat(b.color),children:["评估结果：",b.title]}),(0,l.jsxs)("div",{className:"text-3xl font-bold text-gray-900",children:["总分：",h,"/27"]})]}),(0,l.jsxs)(c.Wu,{className:"pt-6",children:[(0,l.jsx)("p",{className:"text-gray-700 mb-6",children:b.description}),(0,l.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:[(0,l.jsx)("h4",{className:"font-semibold text-yellow-800 mb-2",children:"重要提醒"}),(0,l.jsx)("p",{className:"text-yellow-700 text-sm",children:"本评估仅供参考，不能替代专业医疗诊断。如果您感到困扰或有自伤想法，请立即寻求专业帮助。"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(d.$,{onClick:()=>{r(0),u(Array(R.$Z.length).fill(-1)),m(!1),x(!1)},variant:"outline",className:"w-full",children:"重新评估"}),(0,l.jsx)(d.$,{variant:"warm",className:"w-full",children:"寻求帮助"})]})]})]}),(0,l.jsxs)(c.Zp,{children:[(0,l.jsx)(c.aR,{children:(0,l.jsx)(c.ZB,{children:"您的答案回顾"})}),(0,l.jsx)(c.Wu,{children:(0,l.jsx)("div",{className:"space-y-4",children:R.$Z.map((e,r)=>{var a;return(0,l.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600 flex-1",children:e.question}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-900 ml-4",children:(null==(a=R.C1[t[r]])?void 0:a.label)||"未回答"})]},e.id)})})})]})]})})}):(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-12",children:(0,l.jsx)("div",{className:"mx-auto max-w-4xl px-6",children:(0,l.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,l.jsx)(c.Zp,{className:"mb-8",children:(0,l.jsxs)(c.aR,{className:"text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(s.A,{className:"w-8 h-8 text-white"})}),(0,l.jsx)(c.ZB,{className:"text-2xl",children:"PHQ-9 抑郁症评估"}),(0,l.jsx)(c.BT,{children:"请根据过去两周的感受，选择最符合您情况的选项"})]})}),(0,l.jsx)(c.Zp,{className:"mb-8",children:(0,l.jsxs)(c.Wu,{className:"pt-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,l.jsxs)("span",{className:"text-sm text-gray-600",children:["问题 ",e+1," / ",R.$Z.length]}),(0,l.jsxs)("span",{className:"text-sm text-gray-600",children:[Math.round(f),"% 完成"]})]}),(0,l.jsx)(A,{value:f,className:"h-2"})]})}),(0,l.jsx)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5},children:(0,l.jsxs)(c.Zp,{className:"mb-8",children:[(0,l.jsxs)(c.aR,{children:[(0,l.jsx)(c.ZB,{className:"text-xl",children:R.$Z[e].question}),(0,l.jsx)(c.BT,{children:R.$Z[e].description})]}),(0,l.jsx)(c.Wu,{children:(0,l.jsx)("div",{className:"space-y-3",children:R.C1.map(r=>(0,l.jsx)(n.P.button,{onClick:()=>v(r.value),className:"w-full p-4 text-left rounded-lg border-2 transition-all hover:border-blue-300 hover:bg-blue-50 ".concat(t[e]===r.value?"border-blue-500 bg-blue-50":"border-gray-200 bg-white"),whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium text-gray-900",children:r.label}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:r.days})]}),(0,l.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:r.value})]})},r.value))})})]})},e),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsxs)(d.$,{onClick:()=>{e>0&&r(e-1)},variant:"outline",disabled:0===e,className:"flex items-center space-x-2",children:[(0,l.jsx)(o.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"上一题"})]}),(0,l.jsx)("div",{className:"text-sm text-gray-500 flex items-center",children:"点击选项自动进入下一题"})]})]})})})}A.displayName=y.displayName}},e=>{var r=r=>e(e.s=r);e.O(0,[778,934,441,684,358],()=>r(18475)),_N_E=e.O()}]);