(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[457],{26766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(95155),s=r(12115),l=r(51934),i=r(71366),o=r(19946);let n=(0,o.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),d=(0,o.A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);var c=r(90105),m=r(51976);let u=(0,o.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);var x=r(30285),p=r(66695);let h=["我听到了你的话，感谢你愿意和我分享。你现在的感受是完全可以理解的。","每个人都有低落的时候，这并不意味着你有什么问题。你愿意告诉我更多吗？","你的感受很重要，我想更好地理解你现在的状况。","听起来你正在经历一些困难。记住，寻求帮助是勇敢的表现。","我能感受到你的痛苦。虽然我是AI，但我希望能给你一些温暖和支持。","你不是一个人在面对这些。有很多人关心你，包括专业的帮助者。","每一天都是新的开始。即使现在很难，但请相信事情会慢慢好起来的。","你已经很勇敢了，能够表达自己的感受需要很大的勇气。"],f=["我感到很沮丧","今天过得不太好","我觉得很孤独","我想找人聊聊","我需要一些鼓励","我感到焦虑不安"];function g(){let[e,t]=(0,s.useState)([{id:"1",content:"你好！我是你的AI陪伴者小光。我在这里倾听你的心声，陪伴你度过困难时光。请随时告诉我你的感受，我会尽我所能给你支持和理解。",sender:"ai",timestamp:new Date}]),[r,o]=(0,s.useState)(""),[g,y]=(0,s.useState)(!1),b=(0,s.useRef)(null),v=()=>{var e;null==(e=b.current)||e.scrollIntoView({behavior:"smooth"})};(0,s.useEffect)(()=>{v()},[e]);let j=async e=>{if(!e.trim())return;let r={id:Date.now().toString(),content:e.trim(),sender:"user",timestamp:new Date};t(e=>[...e,r]),o(""),y(!0),setTimeout(()=>{let e={id:(Date.now()+1).toString(),content:h[Math.floor(Math.random()*h.length)],sender:"ai",timestamp:new Date};t(t=>[...t,e]),y(!1)},1e3+2e3*Math.random())},N=e=>{j(e)};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50",children:(0,a.jsxs)("div",{className:"mx-auto max-w-4xl px-6 py-8",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:(0,a.jsx)(p.Zp,{className:"mb-6",children:(0,a.jsxs)(p.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)(p.ZB,{className:"text-2xl",children:"AI陪聊"}),(0,a.jsx)(p.BT,{children:"24小时温暖陪伴，倾听你的心声"})]})})}),(0,a.jsxs)(p.Zp,{className:"h-[600px] flex flex-col",children:[(0,a.jsxs)(p.Wu,{className:"flex-1 overflow-y-auto p-6 space-y-4",children:[e.map(e=>(0,a.jsx)(l.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:"flex ".concat("user"===e.sender?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 max-w-[80%] ".concat("user"===e.sender?"flex-row-reverse space-x-reverse":""),children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("user"===e.sender?"bg-blue-500":"bg-gradient-to-br from-purple-500 to-pink-600"),children:"user"===e.sender?(0,a.jsx)(n,{className:"w-4 h-4 text-white"}):(0,a.jsx)(d,{className:"w-4 h-4 text-white"})}),(0,a.jsxs)("div",{className:"rounded-lg p-3 ".concat("user"===e.sender?"bg-blue-500 text-white":"bg-gray-100 text-gray-900"),children:[(0,a.jsx)("p",{className:"text-sm",children:e.content}),(0,a.jsx)("p",{className:"text-xs mt-1 ".concat("user"===e.sender?"text-blue-100":"text-gray-500"),children:e.timestamp.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"})})]})]})},e.id)),g&&(0,a.jsx)(l.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex justify-start",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center",children:(0,a.jsx)(d,{className:"w-4 h-4 text-white"})}),(0,a.jsx)("div",{className:"bg-gray-100 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]})}),(0,a.jsx)("div",{ref:b})]}),1===e.length&&(0,a.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"快速开始对话："}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:f.map((e,t)=>(0,a.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>N(e),className:"text-xs",children:e},t))})]}),(0,a.jsxs)("div",{className:"p-6 border-t border-gray-200",children:[(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),j(r)},className:"flex space-x-3",children:[(0,a.jsx)("input",{type:"text",value:r,onChange:e=>o(e.target.value),placeholder:"输入你想说的话...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",disabled:g}),(0,a.jsx)(x.$,{type:"submit",disabled:!r.trim()||g,className:"bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700",children:(0,a.jsx)(c.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2 text-center",children:[(0,a.jsx)(m.A,{className:"w-3 h-3 inline mr-1"}),"AI陪聊仅供情感支持，不能替代专业心理咨询"]})]})]}),(0,a.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-6",children:(0,a.jsx)(p.Zp,{className:"bg-yellow-50 border-yellow-200",children:(0,a.jsx)(p.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(u,{className:"w-5 h-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-yellow-800 mb-1",children:"温馨提示"}),(0,a.jsx)("p",{className:"text-sm text-yellow-700",children:"我是AI陪伴者，可以倾听和陪伴，但无法提供专业的心理治疗。如果您需要专业帮助，请联系心理咨询师或医生。"})]})]})})})})]})})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var a=r(95155),s=r(12115),l=r(74466),i=r(59434);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",warm:"bg-gradient-to-r from-orange-400 to-pink-400 text-white hover:from-orange-500 hover:to-pink-500",calm:"bg-gradient-to-r from-blue-400 to-purple-400 text-white hover:from-blue-500 hover:to-purple-500"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=s.forwardRef((e,t)=>{let{className:r,variant:s,size:l,...n}=e;return(0,a.jsx)("button",{className:(0,i.cn)(o({variant:s,size:l,className:r})),ref:t,...n})});n.displayName="Button"},51976:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{$C:()=>m,$Z:()=>i,C1:()=>o,IG:()=>u,Yq:()=>c,_o:()=>d,aX:()=>n,cn:()=>l});var a=r(52596),s=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}let i=[{id:1,question:"做事时提不起劲或没有兴趣",description:"在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"},{id:2,question:"感到心情低落、沮丧或绝望",description:"在过去两周内，您有多少天感到心情低落、沮丧或绝望？"},{id:3,question:"入睡困难、睡不安稳或睡眠过多",description:"在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"},{id:4,question:"感觉疲倦或没有活力",description:"在过去两周内，您有多少天感觉疲倦或没有活力？"},{id:5,question:"食欲不振或吃太多",description:"在过去两周内，您有多少天有食欲不振或吃太多的问题？"},{id:6,question:"觉得自己很糟糕或觉得自己很失败",description:"在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"},{id:7,question:"对事物专注有困难",description:"在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"},{id:8,question:"动作或说话速度缓慢或烦躁不安",description:"在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"},{id:9,question:"有不如死掉或伤害自己的念头",description:"在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"}],o=[{value:0,label:"完全没有",days:"0天"},{value:1,label:"几天",days:"1-6天"},{value:2,label:"一半以上的天数",days:"7-11天"},{value:3,label:"几乎每天",days:"12-14天"}];function n(e){return e<=4?{level:"minimal",title:"轻微或无抑郁症状",description:"您目前的症状较轻微，建议继续保持良好的生活习惯。",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"}:e<=9?{level:"mild",title:"轻度抑郁症状",description:"您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}:e<=14?{level:"moderate",title:"中度抑郁症状",description:"您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"}:e<=19?{level:"moderately-severe",title:"中重度抑郁症状",description:"您的症状较为严重，强烈建议寻求专业的心理健康服务。",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}:{level:"severe",title:"重度抑郁症状",description:"您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",color:"text-red-800",bgColor:"bg-red-100",borderColor:"border-red-300"}}let d=[{id:"very-happy",label:"非常开心",emoji:"\uD83D\uDE04",color:"#10b981"},{id:"happy",label:"开心",emoji:"\uD83D\uDE0A",color:"#34d399"},{id:"neutral",label:"平静",emoji:"\uD83D\uDE10",color:"#6b7280"},{id:"sad",label:"难过",emoji:"\uD83D\uDE22",color:"#f59e0b"},{id:"very-sad",label:"非常难过",emoji:"\uD83D\uDE2D",color:"#ef4444"},{id:"anxious",label:"焦虑",emoji:"\uD83D\uDE30",color:"#8b5cf6"},{id:"angry",label:"愤怒",emoji:"\uD83D\uDE20",color:"#dc2626"},{id:"tired",label:"疲惫",emoji:"\uD83D\uDE34",color:"#64748b"}];function c(e){return e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}function m(){return Math.random().toString(36).substr(2,9)}let u={get:e=>{try{let t=localStorage.getItem(e);return t?JSON.parse(t):null}catch(e){return null}},set:(e,t)=>{try{localStorage.setItem(e,JSON.stringify(t))}catch(e){}},remove:e=>{try{localStorage.removeItem(e)}catch(e){}}}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>o});var a=r(95155),s=r(12115),l=r(59434);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});i.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...s})});o.displayName="CardHeader";let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});n.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},71366:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},90105:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},96863:(e,t,r)=>{Promise.resolve().then(r.bind(r,26766))}},e=>{var t=t=>e(e.s=t);e.O(0,[778,934,441,684,358],()=>t(96863)),_N_E=e.O()}]);