(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[597],{16785:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},30285:(e,s,r)=>{"use strict";r.d(s,{$:()=>c});var l=r(95155),t=r(12115),i=r(74466),a=r(59434);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",warm:"bg-gradient-to-r from-orange-400 to-pink-400 text-white hover:from-orange-500 hover:to-pink-500",calm:"bg-gradient-to-r from-blue-400 to-purple-400 text-white hover:from-blue-500 hover:to-purple-500"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,s)=>{let{className:r,variant:t,size:i,...c}=e;return(0,l.jsx)("button",{className:(0,a.cn)(d({variant:t,size:i,className:r})),ref:s,...c})});c.displayName="Button"},35169:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},51976:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},57434:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59434:(e,s,r)=>{"use strict";r.d(s,{$C:()=>x,$Z:()=>a,C1:()=>d,IG:()=>h,Yq:()=>o,_o:()=>n,aX:()=>c,cn:()=>i});var l=r(52596),t=r(39688);function i(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.QP)((0,l.$)(s))}let a=[{id:1,question:"做事时提不起劲或没有兴趣",description:"在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"},{id:2,question:"感到心情低落、沮丧或绝望",description:"在过去两周内，您有多少天感到心情低落、沮丧或绝望？"},{id:3,question:"入睡困难、睡不安稳或睡眠过多",description:"在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"},{id:4,question:"感觉疲倦或没有活力",description:"在过去两周内，您有多少天感觉疲倦或没有活力？"},{id:5,question:"食欲不振或吃太多",description:"在过去两周内，您有多少天有食欲不振或吃太多的问题？"},{id:6,question:"觉得自己很糟糕或觉得自己很失败",description:"在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"},{id:7,question:"对事物专注有困难",description:"在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"},{id:8,question:"动作或说话速度缓慢或烦躁不安",description:"在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"},{id:9,question:"有不如死掉或伤害自己的念头",description:"在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"}],d=[{value:0,label:"完全没有",days:"0天"},{value:1,label:"几天",days:"1-6天"},{value:2,label:"一半以上的天数",days:"7-11天"},{value:3,label:"几乎每天",days:"12-14天"}];function c(e){return e<=4?{level:"minimal",title:"轻微或无抑郁症状",description:"您目前的症状较轻微，建议继续保持良好的生活习惯。",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"}:e<=9?{level:"mild",title:"轻度抑郁症状",description:"您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}:e<=14?{level:"moderate",title:"中度抑郁症状",description:"您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"}:e<=19?{level:"moderately-severe",title:"中重度抑郁症状",description:"您的症状较为严重，强烈建议寻求专业的心理健康服务。",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}:{level:"severe",title:"重度抑郁症状",description:"您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",color:"text-red-800",bgColor:"bg-red-100",borderColor:"border-red-300"}}let n=[{id:"very-happy",label:"非常开心",emoji:"\uD83D\uDE04",color:"#10b981"},{id:"happy",label:"开心",emoji:"\uD83D\uDE0A",color:"#34d399"},{id:"neutral",label:"平静",emoji:"\uD83D\uDE10",color:"#6b7280"},{id:"sad",label:"难过",emoji:"\uD83D\uDE22",color:"#f59e0b"},{id:"very-sad",label:"非常难过",emoji:"\uD83D\uDE2D",color:"#ef4444"},{id:"anxious",label:"焦虑",emoji:"\uD83D\uDE30",color:"#8b5cf6"},{id:"angry",label:"愤怒",emoji:"\uD83D\uDE20",color:"#dc2626"},{id:"tired",label:"疲惫",emoji:"\uD83D\uDE34",color:"#64748b"}];function o(e){return e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}function x(){return Math.random().toString(36).substr(2,9)}let h={get:e=>{try{let s=localStorage.getItem(e);return s?JSON.parse(s):null}catch(e){return null}},set:(e,s)=>{try{localStorage.setItem(e,JSON.stringify(s))}catch(e){}},remove:e=>{try{localStorage.removeItem(e)}catch(e){}}}},66695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>n,Wu:()=>o,ZB:()=>c,Zp:()=>a,aR:()=>d});var l=r(95155),t=r(12115),i=r(59434);let a=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...t})});a.displayName="Card";let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...t})});d.displayName="CardHeader";let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...t})});c.displayName="CardTitle";let n=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",r),...t})});n.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",r),...t})});o.displayName="CardContent",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",r),...t})}).displayName="CardFooter"},70463:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},86811:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var l=r(95155),t=r(51934),i=r(35169),a=r(57434),d=r(16785),c=r(70463),n=r(51976),o=r(17580),x=r(6874),h=r.n(x),m=r(30285),p=r(66695);function j(){return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50",children:(0,l.jsxs)("div",{className:"mx-auto max-w-4xl px-6 py-8",children:[(0,l.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsx)(m.$,{variant:"ghost",asChild:!0,className:"mb-4",children:(0,l.jsxs)(h(),{href:"/about",className:"flex items-center space-x-2",children:[(0,l.jsx)(i.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"返回关于我们"})]})})}),(0,l.jsx)(p.Zp,{className:"mb-8",children:(0,l.jsxs)(p.aR,{className:"text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(a.A,{className:"w-8 h-8 text-white"})}),(0,l.jsx)(p.ZB,{className:"text-3xl",children:"项目初衷"}),(0,l.jsx)(p.BT,{className:"text-lg",children:"YYZ（抑郁症）Web应用产品需求与开发文档"})]})})]}),(0,l.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"space-y-8",children:[(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(d.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"项目背景"})]})}),(0,l.jsxs)(p.Wu,{className:"prose prose-gray max-w-none",children:[(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"抑郁症是一种常见的心理疾病，全球有超过3.5亿人受其影响。在中国，抑郁症的患病率约为4.2%， 但由于社会认知不足、就医门槛高等问题，大多数患者无法得到及时有效的帮助。"}),(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"我们发现，传统的心理健康服务存在以下问题："}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2",children:[(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"可及性差"}),"：专业心理咨询师资源稀缺，预约困难"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"成本高昂"}),"：心理咨询费用对普通人群负担较重"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"隐私担忧"}),"：面对面咨询让很多人感到不自在"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"缺乏连续性"}),"：缺少日常的情绪监测和支持"]})]})]})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(c.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"解决方案"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"技术创新"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• AI驱动的智能陪伴系统"}),(0,l.jsx)("li",{children:"• 基于PHQ-9的专业评估工具"}),(0,l.jsx)("li",{children:"• 数据可视化的情绪追踪"}),(0,l.jsx)("li",{children:"• 个性化的内容推荐"})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"服务理念"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 24/7全天候可用"}),(0,l.jsx)("li",{children:"• 完全免费的公益服务"}),(0,l.jsx)("li",{children:"• 匿名使用保护隐私"}),(0,l.jsx)("li",{children:"• 专业志愿者支持"})]})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(n.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"核心功能模块"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"1. 心理评估系统"}),(0,l.jsx)("p",{className:"text-blue-700 text-sm",children:"基于PHQ-9量表的标准化抑郁症评估，提供专业的结果解读和建议。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-purple-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-purple-900 mb-2",children:"2. AI陪伴聊天"}),(0,l.jsx)("p",{className:"text-purple-700 text-sm",children:"智能对话系统提供情感支持，24小时倾听用户心声。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"3. 情绪日志"}),(0,l.jsx)("p",{className:"text-green-700 text-sm",children:"记录日常情绪变化，通过数据分析帮助用户了解情绪模式。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"4. 科普教育"}),(0,l.jsx)("p",{className:"text-orange-700 text-sm",children:"提供权威的抑郁症相关知识，帮助用户正确认识心理健康。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-teal-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-teal-900 mb-2",children:"5. 医疗数据管理"}),(0,l.jsx)("p",{className:"text-teal-700 text-sm",children:"整合健康记录，生成专业报告，便于与医生沟通。"})]}),(0,l.jsxs)("div",{className:"p-4 bg-pink-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-pink-900 mb-2",children:"6. 社区支持"}),(0,l.jsx)("p",{className:"text-pink-700 text-sm",children:"志愿者网络和用户社区，提供人文关怀和互助支持。"})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(o.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"目标用户群体"})]})}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 mx-auto mb-3 bg-blue-100 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-blue-600 font-bold",children:"1"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"轻度抑郁症患者"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"需要早期干预和日常支持的用户"})]}),(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 mx-auto mb-3 bg-purple-100 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-purple-600 font-bold",children:"2"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"高风险人群"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"学生、职场人士等压力较大的群体"})]}),(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 mx-auto mb-3 bg-green-100 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-green-600 font-bold",children:"3"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"关注者"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"患者家属、朋友及心理健康关注者"})]})]})})]}),(0,l.jsxs)(p.Zp,{children:[(0,l.jsx)(p.aR,{children:(0,l.jsx)(p.ZB,{children:"技术架构"})}),(0,l.jsx)(p.Wu,{children:(0,l.jsx)("div",{className:"bg-gray-50 p-6 rounded-lg",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"前端技术栈"}),(0,l.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,l.jsx)("li",{children:"• Next.js 15 + React 18"}),(0,l.jsx)("li",{children:"• TypeScript"}),(0,l.jsx)("li",{children:"• TailwindCSS + Shadcn UI"}),(0,l.jsx)("li",{children:"• Framer Motion"}),(0,l.jsx)("li",{children:"• Recharts"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"后端规划"}),(0,l.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,l.jsx)("li",{children:"• Node.js + Express"}),(0,l.jsx)("li",{children:"• PostgreSQL 数据库"}),(0,l.jsx)("li",{children:"• OpenAI GPT API"}),(0,l.jsx)("li",{children:"• Redis 缓存"}),(0,l.jsx)("li",{children:"• Docker 容器化"})]})]})]})})})]}),(0,l.jsx)(p.Zp,{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,l.jsx)(p.Wu,{className:"pt-6",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"text-xl font-bold mb-4",children:"加入我们的使命"}),(0,l.jsx)("p",{className:"mb-6 text-blue-100",children:"如果您认同我们的理念，愿意为心理健康事业贡献力量，欢迎加入我们！"}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,l.jsx)(m.$,{size:"lg",variant:"secondary",asChild:!0,children:(0,l.jsx)(h(),{href:"/join",children:"成为志愿者"})}),(0,l.jsx)(m.$,{size:"lg",variant:"outline",className:"text-white border-white hover:bg-white hover:text-blue-600",asChild:!0,children:(0,l.jsx)(h(),{href:"/about",children:"返回关于我们"})})]})]})})})]})]})})}},95877:(e,s,r)=>{Promise.resolve().then(r.bind(r,86811))}},e=>{var s=s=>e(e.s=s);e.O(0,[778,934,874,441,684,358],()=>s(95877)),_N_E=e.O()}]);