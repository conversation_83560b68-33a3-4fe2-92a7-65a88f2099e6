(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[272],{3026:(e,s,r)=>{Promise.resolve().then(r.bind(r,43786))},5040:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},17580:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},30285:(e,s,r)=>{"use strict";r.d(s,{$:()=>c});var l=r(95155),t=r(12115),a=r(74466),i=r(59434);let d=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",warm:"bg-gradient-to-r from-orange-400 to-pink-400 text-white hover:from-orange-500 hover:to-pink-500",calm:"bg-gradient-to-r from-blue-400 to-purple-400 text-white hover:from-blue-500 hover:to-purple-500"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,s)=>{let{className:r,variant:t,size:a,...c}=e;return(0,l.jsx)("button",{className:(0,i.cn)(d({variant:t,size:a,className:r})),ref:s,...c})});c.displayName="Button"},34869:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},35169:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},43786:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var l=r(95155),t=r(51934),a=r(35169),i=r(5040),d=r(72713),c=r(17580),n=r(75525),o=r(34869),x=r(6874),h=r.n(x),m=r(30285),j=r(66695);function u(){return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50",children:(0,l.jsxs)("div",{className:"mx-auto max-w-4xl px-6 py-8",children:[(0,l.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsx)(m.$,{variant:"ghost",asChild:!0,className:"mb-4",children:(0,l.jsxs)(h(),{href:"/about",className:"flex items-center space-x-2",children:[(0,l.jsx)(a.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"返回关于我们"})]})})}),(0,l.jsx)(j.Zp,{className:"mb-8",children:(0,l.jsxs)(j.aR,{className:"text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,l.jsx)(j.ZB,{className:"text-3xl",children:"项目白皮书"}),(0,l.jsx)(j.BT,{className:"text-lg",children:"YYZ抑郁症研究与帮助平台：数字化心理健康服务的创新实践"})]})})]}),(0,l.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"space-y-8",children:[(0,l.jsxs)(j.Zp,{children:[(0,l.jsx)(j.aR,{children:(0,l.jsx)(j.ZB,{children:"执行摘要"})}),(0,l.jsxs)(j.Wu,{className:"prose prose-gray max-w-none",children:[(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"YYZ项目是一个创新的数字化心理健康服务平台，专注于为抑郁症患者及高风险人群提供 全方位的支持服务。通过整合人工智能、专业心理学知识和社区力量，我们致力于降低 心理健康服务的门槛，提高服务的可及性和有效性。"}),(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"本白皮书详细阐述了项目的社会价值、技术创新、商业模式和发展规划，为投资者、 合作伙伴和社会各界了解项目提供全面的参考。"})]})]}),(0,l.jsxs)(j.Zp,{children:[(0,l.jsx)(j.aR,{children:(0,l.jsxs)(j.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(d.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"市场分析"})]})}),(0,l.jsx)(j.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"市场规模"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg",children:[(0,l.jsx)("span",{className:"text-sm font-medium",children:"全球抑郁症患者"}),(0,l.jsx)("span",{className:"text-blue-600 font-bold",children:"3.5亿+"})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center p-3 bg-purple-50 rounded-lg",children:[(0,l.jsx)("span",{className:"text-sm font-medium",children:"中国患病率"}),(0,l.jsx)("span",{className:"text-purple-600 font-bold",children:"4.2%"})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center p-3 bg-green-50 rounded-lg",children:[(0,l.jsx)("span",{className:"text-sm font-medium",children:"就医率"}),(0,l.jsx)("span",{className:"text-green-600 font-bold",children:"仅10%"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"市场痛点"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 专业心理咨询师资源严重不足"}),(0,l.jsx)("li",{children:"• 传统服务成本高昂，普及率低"}),(0,l.jsx)("li",{children:"• 社会认知不足，存在偏见和歧视"}),(0,l.jsx)("li",{children:"• 缺乏有效的早期筛查和干预机制"}),(0,l.jsx)("li",{children:"• 数字化服务质量参差不齐"})]})]})]})})]}),(0,l.jsxs)(j.Zp,{children:[(0,l.jsx)(j.aR,{children:(0,l.jsx)(j.ZB,{children:"创新亮点"})}),(0,l.jsx)(j.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"p-4 border border-blue-200 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"\uD83E\uDD16 AI技术创新"}),(0,l.jsx)("p",{className:"text-blue-700 text-sm",children:"基于大语言模型的智能陪伴系统，提供个性化的情感支持和专业建议。"})]}),(0,l.jsxs)("div",{className:"p-4 border border-purple-200 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-purple-900 mb-2",children:"\uD83D\uDCCA 数据驱动"}),(0,l.jsx)("p",{className:"text-purple-700 text-sm",children:"通过情绪追踪和行为分析，为用户提供科学的心理健康洞察。"})]}),(0,l.jsxs)("div",{className:"p-4 border border-green-200 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"\uD83C\uDFE5 医疗整合"}),(0,l.jsx)("p",{className:"text-green-700 text-sm",children:"与传统医疗体系无缝对接，为线下就医提供数据支持。"})]}),(0,l.jsxs)("div",{className:"p-4 border border-orange-200 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"\uD83C\uDF10 社区生态"}),(0,l.jsx)("p",{className:"text-orange-700 text-sm",children:"构建专业志愿者网络，形成可持续的社区支持体系。"})]})]})})]}),(0,l.jsxs)(j.Zp,{children:[(0,l.jsx)(j.aR,{children:(0,l.jsxs)(j.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(c.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"社会价值"})]})}),(0,l.jsx)(j.Wu,{children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white text-2xl font-bold",children:"1M+"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"预期服务用户"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"3年内服务超过100万用户"})]}),(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white text-2xl font-bold",children:"24/7"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"全天候服务"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"打破时间和地域限制"})]}),(0,l.jsxs)("div",{className:"text-center p-4",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white text-2xl font-bold",children:"0\xa5"})}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"完全免费"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"降低心理健康服务门槛"})]})]}),(0,l.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"预期社会效益"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"提高早期识别率"}),"：通过便捷的评估工具，帮助更多人及早发现心理健康问题"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"减少社会成本"}),"：预防性干预降低重度抑郁症的治疗成本"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"促进社会认知"}),"：通过科普教育消除对心理疾病的偏见"]}),(0,l.jsxs)("li",{children:["• ",(0,l.jsx)("strong",{children:"培养专业人才"}),"：为心理健康领域培养更多志愿者和专业人士"]})]})]})]})})]}),(0,l.jsxs)(j.Zp,{children:[(0,l.jsx)(j.aR,{children:(0,l.jsxs)(j.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(n.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"隐私与安全"})]})}),(0,l.jsx)(j.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"数据保护措施"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 端到端加密传输"}),(0,l.jsx)("li",{children:"• 本地数据存储优先"}),(0,l.jsx)("li",{children:"• 匿名化数据处理"}),(0,l.jsx)("li",{children:"• 定期安全审计"}),(0,l.jsx)("li",{children:"• 符合GDPR等国际标准"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"用户权利保障"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 完全匿名使用权"}),(0,l.jsx)("li",{children:"• 数据删除权"}),(0,l.jsx)("li",{children:"• 服务退出权"}),(0,l.jsx)("li",{children:"• 透明的隐私政策"}),(0,l.jsx)("li",{children:"• 用户数据控制权"})]})]})]})})]}),(0,l.jsxs)(j.Zp,{children:[(0,l.jsx)(j.aR,{children:(0,l.jsxs)(j.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(o.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"发展规划"})]})}),(0,l.jsx)(j.Wu,{children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"短期目标（6-12个月）"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 完成MVP开发和测试"}),(0,l.jsx)("li",{children:"• 招募100名专业志愿者"}),(0,l.jsx)("li",{children:"• 服务1万名用户"}),(0,l.jsx)("li",{children:"• 建立合作伙伴网络"})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"中期目标（1-3年）"}),(0,l.jsxs)("ul",{className:"text-gray-700 space-y-2 text-sm",children:[(0,l.jsx)("li",{children:"• 用户规模达到100万"}),(0,l.jsx)("li",{children:"• 推出移动应用"}),(0,l.jsx)("li",{children:"• 与医疗机构深度合作"}),(0,l.jsx)("li",{children:"• 开展学术研究合作"})]})]})]}),(0,l.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"长期愿景（3-5年）"}),(0,l.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:"成为中国领先的数字化心理健康服务平台，建立覆盖全国的志愿者网络， 与政府、医疗机构、高校等建立深度合作关系，推动心理健康服务的 标准化和普及化，为构建健康中国贡献力量。"})]})]})})]}),(0,l.jsx)(j.Zp,{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,l.jsx)(j.Wu,{className:"pt-6",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"text-xl font-bold mb-4",children:"携手共建心理健康生态"}),(0,l.jsx)("p",{className:"mb-6 text-blue-100",children:"我们诚邀各界人士加入这一有意义的事业，共同为心理健康服务的创新发展贡献力量。"}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,l.jsx)(m.$,{size:"lg",variant:"secondary",asChild:!0,children:(0,l.jsx)(h(),{href:"/join",children:"加入我们"})}),(0,l.jsx)(m.$,{size:"lg",variant:"outline",className:"text-white border-white hover:bg-white hover:text-blue-600",asChild:!0,children:(0,l.jsx)(h(),{href:"/docs/project-vision",children:"查看项目初衷"})})]})]})})})]})]})})}},59434:(e,s,r)=>{"use strict";r.d(s,{$C:()=>x,$Z:()=>i,C1:()=>d,IG:()=>h,Yq:()=>o,_o:()=>n,aX:()=>c,cn:()=>a});var l=r(52596),t=r(39688);function a(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.QP)((0,l.$)(s))}let i=[{id:1,question:"做事时提不起劲或没有兴趣",description:"在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"},{id:2,question:"感到心情低落、沮丧或绝望",description:"在过去两周内，您有多少天感到心情低落、沮丧或绝望？"},{id:3,question:"入睡困难、睡不安稳或睡眠过多",description:"在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"},{id:4,question:"感觉疲倦或没有活力",description:"在过去两周内，您有多少天感觉疲倦或没有活力？"},{id:5,question:"食欲不振或吃太多",description:"在过去两周内，您有多少天有食欲不振或吃太多的问题？"},{id:6,question:"觉得自己很糟糕或觉得自己很失败",description:"在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"},{id:7,question:"对事物专注有困难",description:"在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"},{id:8,question:"动作或说话速度缓慢或烦躁不安",description:"在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"},{id:9,question:"有不如死掉或伤害自己的念头",description:"在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"}],d=[{value:0,label:"完全没有",days:"0天"},{value:1,label:"几天",days:"1-6天"},{value:2,label:"一半以上的天数",days:"7-11天"},{value:3,label:"几乎每天",days:"12-14天"}];function c(e){return e<=4?{level:"minimal",title:"轻微或无抑郁症状",description:"您目前的症状较轻微，建议继续保持良好的生活习惯。",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"}:e<=9?{level:"mild",title:"轻度抑郁症状",description:"您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}:e<=14?{level:"moderate",title:"中度抑郁症状",description:"您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"}:e<=19?{level:"moderately-severe",title:"中重度抑郁症状",description:"您的症状较为严重，强烈建议寻求专业的心理健康服务。",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}:{level:"severe",title:"重度抑郁症状",description:"您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",color:"text-red-800",bgColor:"bg-red-100",borderColor:"border-red-300"}}let n=[{id:"very-happy",label:"非常开心",emoji:"\uD83D\uDE04",color:"#10b981"},{id:"happy",label:"开心",emoji:"\uD83D\uDE0A",color:"#34d399"},{id:"neutral",label:"平静",emoji:"\uD83D\uDE10",color:"#6b7280"},{id:"sad",label:"难过",emoji:"\uD83D\uDE22",color:"#f59e0b"},{id:"very-sad",label:"非常难过",emoji:"\uD83D\uDE2D",color:"#ef4444"},{id:"anxious",label:"焦虑",emoji:"\uD83D\uDE30",color:"#8b5cf6"},{id:"angry",label:"愤怒",emoji:"\uD83D\uDE20",color:"#dc2626"},{id:"tired",label:"疲惫",emoji:"\uD83D\uDE34",color:"#64748b"}];function o(e){return e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}function x(){return Math.random().toString(36).substr(2,9)}let h={get:e=>{try{let s=localStorage.getItem(e);return s?JSON.parse(s):null}catch(e){return null}},set:(e,s)=>{try{localStorage.setItem(e,JSON.stringify(s))}catch(e){}},remove:e=>{try{localStorage.removeItem(e)}catch(e){}}}},66695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>n,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>d});var l=r(95155),t=r(12115),a=r(59434);let i=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...t})});i.displayName="Card";let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...t})});d.displayName="CardHeader";let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("h3",{ref:s,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",r),...t})});c.displayName="CardTitle";let n=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",r),...t})});n.displayName="CardDescription";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",r),...t})});o.displayName="CardContent",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,l.jsx)("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",r),...t})}).displayName="CardFooter"},72713:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},75525:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[778,934,874,441,684,358],()=>s(3026)),_N_E=e.O()}]);