(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[335],{3601:(e,r,s)=>{Promise.resolve().then(s.bind(s,38860))},17580:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},30285:(e,r,s)=>{"use strict";s.d(r,{$:()=>c});var t=s(95155),l=s(12115),a=s(74466),i=s(59434);let n=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",warm:"bg-gradient-to-r from-orange-400 to-pink-400 text-white hover:from-orange-500 hover:to-pink-500",calm:"bg-gradient-to-r from-blue-400 to-purple-400 text-white hover:from-blue-500 hover:to-purple-500"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=l.forwardRef((e,r)=>{let{className:s,variant:l,size:a,...c}=e;return(0,t.jsx)("button",{className:(0,i.cn)(n({variant:l,size:a,className:s})),ref:r,...c})});c.displayName="Button"},38860:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>v});var t=s(95155),l=s(12115),a=s(51934),i=s(19946);let n=(0,i.A)("stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]),c=(0,i.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),d=(0,i.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),o=(0,i.A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]);var x=s(40646),m=s(17580),u=s(51976),h=s(90105);let p=(0,i.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),g=(0,i.A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]]);var b=s(71366),y=s(30285),j=s(66695);let f=[{icon:n,title:"心理专业支持者",description:"心理学/精神科背景，协助科普内容审核、AI对话引导",skills:["心理学专业","精神科医师","心理咨询师","社会工作者"],color:"from-blue-500 to-cyan-500"},{icon:c,title:"技术开发者",description:"前端/后端/AI开发，构建Web平台和AI陪聊系统",skills:["React/Next.js","Node.js/Python","AI/GPT API","数据库设计"],color:"from-green-500 to-emerald-500"},{icon:d,title:"设计师",description:"视觉设计/UI设计，为应用赋予温暖的视觉体验",skills:["UI/UX设计","品牌设计","插画设计","用户体验"],color:"from-purple-500 to-pink-500"},{icon:o,title:"内容创作者",description:"内容传播/写作/社媒运营，撰写科普文案和项目故事",skills:["内容写作","社媒运营","视频制作","科普传播"],color:"from-orange-500 to-red-500"}];function v(){let[e,r]=(0,l.useState)({name:"",email:"",role:"",skills:"",motivation:"",experience:""}),[s,i]=(0,l.useState)(!1),n=e=>{let{name:s,value:t}=e.target;r(e=>({...e,[s]:t}))};return s?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center",children:(0,t.jsx)(a.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8},className:"max-w-md mx-auto px-6",children:(0,t.jsxs)(j.Zp,{className:"text-center",children:[(0,t.jsxs)(j.aR,{children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center",children:(0,t.jsx)(x.A,{className:"w-8 h-8 text-white"})}),(0,t.jsx)(j.ZB,{className:"text-2xl",children:"申请已提交"}),(0,t.jsx)(j.BT,{children:"感谢您的申请！我们会尽快与您联系。"})]}),(0,t.jsxs)(j.Wu,{children:[(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"您的热情和专业技能对我们来说非常宝贵。我们期待与您一起为心理健康事业贡献力量。"}),(0,t.jsx)(y.$,{onClick:()=>{i(!1),r({name:"",email:"",role:"",skills:"",motivation:"",experience:""})},variant:"outline",className:"w-full",children:"提交另一个申请"})]})]})})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50",children:(0,t.jsxs)("div",{className:"mx-auto max-w-6xl px-6 py-8",children:[(0,t.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:(0,t.jsx)(j.Zp,{className:"mb-8",children:(0,t.jsxs)(j.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,t.jsx)(m.A,{className:"w-8 h-8 text-white"})}),(0,t.jsx)(j.ZB,{className:"text-3xl",children:"加入我们"}),(0,t.jsx)(j.BT,{className:"text-lg",children:"我们相信，一点善意能照亮一片黑暗"})]})})}),(0,t.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:(0,t.jsx)(j.Zp,{className:"mb-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,t.jsx)(j.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(u.A,{className:"w-12 h-12 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"我们的使命"}),(0,t.jsx)("p",{className:"text-lg leading-relaxed max-w-3xl mx-auto",children:'YYZ 是一个以"抑郁症研究与帮助"为核心目标的公益性项目。我们希望以数字技术为桥梁， 让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声， 为困顿者搭建桥梁。'})]})})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsx)("div",{children:(0,t.jsxs)(a.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"我们需要什么样的你？"}),(0,t.jsx)("div",{className:"space-y-4",children:f.map((e,r)=>{let s=e.icon;return(0,t.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*r},children:(0,t.jsx)(j.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,t.jsx)(j.Wu,{className:"pt-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gradient-to-r ".concat(e.color," flex items-center justify-center flex-shrink-0"),children:(0,t.jsx)(s,{className:"w-6 h-6 text-white"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.description}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.skills.map(e=>(0,t.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:e},e))})]})]})})})},e.title)})})]})}),(0,t.jsx)("div",{children:(0,t.jsx)(a.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.6},children:(0,t.jsxs)(j.Zp,{children:[(0,t.jsxs)(j.aR,{children:[(0,t.jsx)(j.ZB,{children:"志愿者申请表"}),(0,t.jsx)(j.BT,{children:"填写下方表单，让我们认识你！"})]}),(0,t.jsx)(j.Wu,{children:(0,t.jsxs)("form",{onSubmit:r=>{r.preventDefault(),console.log("Form submitted:",e),i(!0)},className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"姓名 *"}),(0,t.jsx)("input",{type:"text",name:"name",value:e.name,onChange:n,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的姓名"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"邮箱 *"}),(0,t.jsx)("input",{type:"email",name:"email",value:e.email,onChange:n,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"感兴趣的角色 *"}),(0,t.jsxs)("select",{name:"role",value:e.role,onChange:n,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,t.jsx)("option",{value:"",children:"请选择角色"}),f.map(e=>(0,t.jsx)("option",{value:e.title,children:e.title},e.title))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"技能和经验"}),(0,t.jsx)("textarea",{name:"skills",value:e.skills,onChange:n,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请简述您的相关技能和经验..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"参与动机 *"}),(0,t.jsx)("textarea",{name:"motivation",value:e.motivation,onChange:n,required:!0,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"为什么想要加入我们？您希望为这个项目贡献什么？"})]}),(0,t.jsxs)(y.$,{type:"submit",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"提交申请"]})]})})]})})})]}),(0,t.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"mt-12",children:(0,t.jsxs)(j.Zp,{children:[(0,t.jsxs)(j.aR,{className:"text-center",children:[(0,t.jsxs)(j.ZB,{className:"text-2xl flex items-center justify-center space-x-2",children:[(0,t.jsx)(p,{className:"w-6 h-6"}),(0,t.jsx)("span",{children:"社区事务论坛"})]}),(0,t.jsx)(j.BT,{children:"项目公告、用户反馈与社区交流平台"})]}),(0,t.jsx)(j.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center space-x-2",children:[(0,t.jsx)(g,{className:"w-5 h-5 text-blue-600"}),(0,t.jsx)("span",{children:"项目公告"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 border border-blue-200 rounded-lg bg-blue-50",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"YYZ平台正式上线"}),(0,t.jsx)("span",{className:"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded",children:"置顶"})]}),(0,t.jsx)("p",{className:"text-blue-700 text-sm mb-2",children:"经过团队的不懈努力，YYZ抑郁症研究与帮助平台正式上线！欢迎大家体验各项功能。"}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-blue-600",children:[(0,t.jsx)("span",{children:"发布者：YYZ团队"}),(0,t.jsx)("span",{children:"2024-06-05"})]})]}),(0,t.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"志愿者招募计划启动"}),(0,t.jsx)("p",{className:"text-gray-700 text-sm mb-2",children:"我们正在招募心理专业支持者、技术开发者、设计师等各类志愿者，欢迎加入！"}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,t.jsx)("span",{children:"发布者：人事部"}),(0,t.jsx)("span",{children:"2024-06-03"})]})]}),(0,t.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"功能更新说明"}),(0,t.jsx)("p",{className:"text-gray-700 text-sm mb-2",children:"新增医疗数据管理功能，用户可以更好地管理健康记录和生成医疗报告。"}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,t.jsx)("span",{children:"发布者：技术部"}),(0,t.jsx)("span",{children:"2024-06-01"})]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center space-x-2",children:[(0,t.jsx)(b.A,{className:"w-5 h-5 text-green-600"}),(0,t.jsx)("span",{children:"用户反馈"})]}),(0,t.jsxs)("div",{className:"mb-6 p-4 border border-green-200 rounded-lg bg-green-50",children:[(0,t.jsx)("h4",{className:"font-medium text-green-900 mb-3",children:"提交反馈"}),(0,t.jsxs)("form",{className:"space-y-3",children:[(0,t.jsx)("div",{children:(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-green-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm",children:[(0,t.jsx)("option",{value:"",children:"选择反馈类型"}),(0,t.jsx)("option",{value:"bug",children:"Bug报告"}),(0,t.jsx)("option",{value:"feature",children:"功能建议"}),(0,t.jsx)("option",{value:"improvement",children:"改进建议"}),(0,t.jsx)("option",{value:"other",children:"其他"})]})}),(0,t.jsx)("div",{children:(0,t.jsx)("textarea",{rows:3,className:"w-full px-3 py-2 border border-green-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm",placeholder:"请详细描述您的反馈..."})}),(0,t.jsxs)(y.$,{size:"sm",className:"w-full bg-green-600 hover:bg-green-700",children:[(0,t.jsx)(h.A,{className:"w-3 h-3 mr-2"}),"提交反馈"]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"最近反馈"}),(0,t.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded",children:"功能建议"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"2天前"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:"希望能增加情绪日志的导出功能，方便与医生分享。"}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["状态：",(0,t.jsx)("span",{className:"text-green-600",children:"已采纳"})]})]}),(0,t.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded",children:"改进建议"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"5天前"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:"AI陪聊的回复速度可以再快一些。"}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["状态：",(0,t.jsx)("span",{className:"text-yellow-600",children:"处理中"})]})]}),(0,t.jsxs)("div",{className:"p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-xs bg-red-100 text-red-700 px-2 py-1 rounded",children:"Bug报告"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"1周前"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:"移动端情绪日志页面显示异常。"}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["状态：",(0,t.jsx)("span",{className:"text-green-600",children:"已修复"})]})]})]})]})]})})]})}),(0,t.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},className:"mt-12",children:(0,t.jsx)(j.Zp,{className:"bg-gray-50",children:(0,t.jsx)(j.Wu,{className:"pt-6",children:(0,t.jsxs)("blockquote",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-xl italic text-gray-700 mb-4",children:'"                  "如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。""'}),(0,t.jsx)("footer",{className:"text-gray-500",children:"— YYZ 项目发起人"})]})})})})]})})}},40646:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51976:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},59434:(e,r,s)=>{"use strict";s.d(r,{$C:()=>x,$Z:()=>i,C1:()=>n,IG:()=>m,Yq:()=>o,_o:()=>d,aX:()=>c,cn:()=>a});var t=s(52596),l=s(39688);function a(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,l.QP)((0,t.$)(r))}let i=[{id:1,question:"做事时提不起劲或没有兴趣",description:"在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"},{id:2,question:"感到心情低落、沮丧或绝望",description:"在过去两周内，您有多少天感到心情低落、沮丧或绝望？"},{id:3,question:"入睡困难、睡不安稳或睡眠过多",description:"在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"},{id:4,question:"感觉疲倦或没有活力",description:"在过去两周内，您有多少天感觉疲倦或没有活力？"},{id:5,question:"食欲不振或吃太多",description:"在过去两周内，您有多少天有食欲不振或吃太多的问题？"},{id:6,question:"觉得自己很糟糕或觉得自己很失败",description:"在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"},{id:7,question:"对事物专注有困难",description:"在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"},{id:8,question:"动作或说话速度缓慢或烦躁不安",description:"在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"},{id:9,question:"有不如死掉或伤害自己的念头",description:"在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"}],n=[{value:0,label:"完全没有",days:"0天"},{value:1,label:"几天",days:"1-6天"},{value:2,label:"一半以上的天数",days:"7-11天"},{value:3,label:"几乎每天",days:"12-14天"}];function c(e){return e<=4?{level:"minimal",title:"轻微或无抑郁症状",description:"您目前的症状较轻微，建议继续保持良好的生活习惯。",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"}:e<=9?{level:"mild",title:"轻度抑郁症状",description:"您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}:e<=14?{level:"moderate",title:"中度抑郁症状",description:"您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"}:e<=19?{level:"moderately-severe",title:"中重度抑郁症状",description:"您的症状较为严重，强烈建议寻求专业的心理健康服务。",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}:{level:"severe",title:"重度抑郁症状",description:"您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",color:"text-red-800",bgColor:"bg-red-100",borderColor:"border-red-300"}}let d=[{id:"very-happy",label:"非常开心",emoji:"\uD83D\uDE04",color:"#10b981"},{id:"happy",label:"开心",emoji:"\uD83D\uDE0A",color:"#34d399"},{id:"neutral",label:"平静",emoji:"\uD83D\uDE10",color:"#6b7280"},{id:"sad",label:"难过",emoji:"\uD83D\uDE22",color:"#f59e0b"},{id:"very-sad",label:"非常难过",emoji:"\uD83D\uDE2D",color:"#ef4444"},{id:"anxious",label:"焦虑",emoji:"\uD83D\uDE30",color:"#8b5cf6"},{id:"angry",label:"愤怒",emoji:"\uD83D\uDE20",color:"#dc2626"},{id:"tired",label:"疲惫",emoji:"\uD83D\uDE34",color:"#64748b"}];function o(e){return e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}function x(){return Math.random().toString(36).substr(2,9)}let m={get:e=>{try{let r=localStorage.getItem(e);return r?JSON.parse(r):null}catch(e){return null}},set:(e,r)=>{try{localStorage.setItem(e,JSON.stringify(r))}catch(e){}},remove:e=>{try{localStorage.removeItem(e)}catch(e){}}}},66695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n});var t=s(95155),l=s(12115),a=s(59434);let i=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...l})});i.displayName="Card";let n=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...l})});n.displayName="CardHeader";let c=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...l})});c.displayName="CardTitle";let d=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",s),...l})});d.displayName="CardDescription";let o=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",s),...l})});o.displayName="CardContent",l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"},71366:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},90105:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[778,934,441,684,358],()=>r(3601)),_N_E=e.O()}]);