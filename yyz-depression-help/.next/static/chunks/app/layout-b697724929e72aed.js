(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{17580:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var l=t(95155),s=t(12115),i=t(74466),a=t(59434);let n=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",warm:"bg-gradient-to-r from-orange-400 to-pink-400 text-white hover:from-orange-500 hover:to-pink-500",calm:"bg-gradient-to-r from-blue-400 to-purple-400 text-white hover:from-blue-500 hover:to-purple-500"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,r)=>{let{className:t,variant:s,size:i,...o}=e;return(0,l.jsx)("button",{className:(0,a.cn)(n({variant:s,size:i,className:t})),ref:r,...o})});o.displayName="Button"},30347:()=>{},54974:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,76196,23)),Promise.resolve().then(t.t.bind(t,30347,23)),Promise.resolve().then(t.bind(t,74151))},59434:(e,r,t)=>{"use strict";t.d(r,{$C:()=>m,$Z:()=>a,C1:()=>n,IG:()=>h,Yq:()=>c,_o:()=>d,aX:()=>o,cn:()=>i});var l=t(52596),s=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,l.$)(r))}let a=[{id:1,question:"做事时提不起劲或没有兴趣",description:"在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"},{id:2,question:"感到心情低落、沮丧或绝望",description:"在过去两周内，您有多少天感到心情低落、沮丧或绝望？"},{id:3,question:"入睡困难、睡不安稳或睡眠过多",description:"在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"},{id:4,question:"感觉疲倦或没有活力",description:"在过去两周内，您有多少天感觉疲倦或没有活力？"},{id:5,question:"食欲不振或吃太多",description:"在过去两周内，您有多少天有食欲不振或吃太多的问题？"},{id:6,question:"觉得自己很糟糕或觉得自己很失败",description:"在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"},{id:7,question:"对事物专注有困难",description:"在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"},{id:8,question:"动作或说话速度缓慢或烦躁不安",description:"在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"},{id:9,question:"有不如死掉或伤害自己的念头",description:"在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"}],n=[{value:0,label:"完全没有",days:"0天"},{value:1,label:"几天",days:"1-6天"},{value:2,label:"一半以上的天数",days:"7-11天"},{value:3,label:"几乎每天",days:"12-14天"}];function o(e){return e<=4?{level:"minimal",title:"轻微或无抑郁症状",description:"您目前的症状较轻微，建议继续保持良好的生活习惯。",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"}:e<=9?{level:"mild",title:"轻度抑郁症状",description:"您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}:e<=14?{level:"moderate",title:"中度抑郁症状",description:"您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"}:e<=19?{level:"moderately-severe",title:"中重度抑郁症状",description:"您的症状较为严重，强烈建议寻求专业的心理健康服务。",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}:{level:"severe",title:"重度抑郁症状",description:"您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",color:"text-red-800",bgColor:"bg-red-100",borderColor:"border-red-300"}}let d=[{id:"very-happy",label:"非常开心",emoji:"\uD83D\uDE04",color:"#10b981"},{id:"happy",label:"开心",emoji:"\uD83D\uDE0A",color:"#34d399"},{id:"neutral",label:"平静",emoji:"\uD83D\uDE10",color:"#6b7280"},{id:"sad",label:"难过",emoji:"\uD83D\uDE22",color:"#f59e0b"},{id:"very-sad",label:"非常难过",emoji:"\uD83D\uDE2D",color:"#ef4444"},{id:"anxious",label:"焦虑",emoji:"\uD83D\uDE30",color:"#8b5cf6"},{id:"angry",label:"愤怒",emoji:"\uD83D\uDE20",color:"#dc2626"},{id:"tired",label:"疲惫",emoji:"\uD83D\uDE34",color:"#64748b"}];function c(e){return e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}function m(){return Math.random().toString(36).substr(2,9)}let h={get:e=>{try{let r=localStorage.getItem(e);return r?JSON.parse(r):null}catch(e){return null}},set:(e,r)=>{try{localStorage.setItem(e,JSON.stringify(r))}catch(e){}},remove:e=>{try{localStorage.removeItem(e)}catch(e){}}}},69074:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71366:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},74151:(e,r,t)=>{"use strict";t.d(r,{default:()=>v});var l=t(95155),s=t(12115),i=t(6874),a=t.n(i),n=t(51976),o=t(5040),d=t(49376),c=t(71366),m=t(69074),h=t(17580),u=t(19946);let x=(0,u.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),b=(0,u.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var g=t(30285),p=t(59434);let f=[{name:"首页",href:"/",icon:n.A},{name:"科普知识",href:"/education",icon:o.A},{name:"心理评估",href:"/assessment",icon:d.A},{name:"AI陪聊",href:"/chat",icon:c.A},{name:"情绪日志",href:"/mood",icon:m.A},{name:"加入我们",href:"/join",icon:h.A}];function v(){let[e,r]=(0,s.useState)(!1);return(0,l.jsxs)("header",{className:"bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50",children:[(0,l.jsxs)("nav",{className:"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8","aria-label":"Global",children:[(0,l.jsx)("div",{className:"flex lg:flex-1",children:(0,l.jsxs)(a(),{href:"/",className:"-m-1.5 p-1.5 flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)(n.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("span",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"YYZ"})]})}),(0,l.jsx)("div",{className:"flex lg:hidden",children:(0,l.jsxs)(g.$,{variant:"ghost",size:"icon",onClick:()=>r(!0),className:"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700",children:[(0,l.jsx)("span",{className:"sr-only",children:"打开主菜单"}),(0,l.jsx)(x,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,l.jsx)("div",{className:"hidden lg:flex lg:gap-x-8",children:f.map(e=>{let r=e.icon;return(0,l.jsxs)(a(),{href:e.href,className:"flex items-center space-x-1 text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors",children:[(0,l.jsx)(r,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:e.name})]},e.name)})}),(0,l.jsx)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end",children:(0,l.jsx)(g.$,{variant:"warm",size:"sm",children:"立即开始"})})]}),(0,l.jsx)("div",{className:(0,p.cn)("lg:hidden",e?"fixed inset-0 z-50":"hidden"),children:(0,l.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)(a(),{href:"/",className:"-m-1.5 p-1.5 flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)(n.A,{className:"w-5 h-5 text-white"})}),(0,l.jsx)("span",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"YYZ"})]}),(0,l.jsxs)(g.$,{variant:"ghost",size:"icon",onClick:()=>r(!1),className:"-m-2.5 rounded-md p-2.5 text-gray-700",children:[(0,l.jsx)("span",{className:"sr-only",children:"关闭菜单"}),(0,l.jsx)(b,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,l.jsx)("div",{className:"mt-6 flow-root",children:(0,l.jsxs)("div",{className:"-my-6 divide-y divide-gray-500/10",children:[(0,l.jsx)("div",{className:"space-y-2 py-6",children:f.map(e=>{let t=e.icon;return(0,l.jsxs)(a(),{href:e.href,onClick:()=>r(!1),className:"-mx-3 flex items-center space-x-3 rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50",children:[(0,l.jsx)(t,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:e.name})]},e.name)})}),(0,l.jsx)("div",{className:"py-6",children:(0,l.jsx)(g.$,{variant:"warm",className:"w-full",children:"立即开始"})})]})})]})})]})}},76196:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}}},e=>{var r=r=>e(e.s=r);e.O(0,[888,778,831,441,684,358],()=>r(54974)),_N_E=e.O()}]);