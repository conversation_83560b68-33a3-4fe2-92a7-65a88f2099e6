(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[948],{17976:(e,r,t)=>{Promise.resolve().then(t.bind(t,71518))},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>n});var s=t(95155),a=t(12115),l=t(74466),i=t(59434);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",warm:"bg-gradient-to-r from-orange-400 to-pink-400 text-white hover:from-orange-500 hover:to-pink-500",calm:"bg-gradient-to-r from-blue-400 to-purple-400 text-white hover:from-blue-500 hover:to-purple-500"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=a.forwardRef((e,r)=>{let{className:t,variant:a,size:l,...n}=e;return(0,s.jsx)("button",{className:(0,i.cn)(o({variant:a,size:l,className:t})),ref:r,...n})});n.displayName="Button"},59434:(e,r,t)=>{"use strict";t.d(r,{$C:()=>m,$Z:()=>i,C1:()=>o,IG:()=>x,Yq:()=>c,_o:()=>d,aX:()=>n,cn:()=>l});var s=t(52596),a=t(39688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}let i=[{id:1,question:"做事时提不起劲或没有兴趣",description:"在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"},{id:2,question:"感到心情低落、沮丧或绝望",description:"在过去两周内，您有多少天感到心情低落、沮丧或绝望？"},{id:3,question:"入睡困难、睡不安稳或睡眠过多",description:"在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"},{id:4,question:"感觉疲倦或没有活力",description:"在过去两周内，您有多少天感觉疲倦或没有活力？"},{id:5,question:"食欲不振或吃太多",description:"在过去两周内，您有多少天有食欲不振或吃太多的问题？"},{id:6,question:"觉得自己很糟糕或觉得自己很失败",description:"在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"},{id:7,question:"对事物专注有困难",description:"在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"},{id:8,question:"动作或说话速度缓慢或烦躁不安",description:"在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"},{id:9,question:"有不如死掉或伤害自己的念头",description:"在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"}],o=[{value:0,label:"完全没有",days:"0天"},{value:1,label:"几天",days:"1-6天"},{value:2,label:"一半以上的天数",days:"7-11天"},{value:3,label:"几乎每天",days:"12-14天"}];function n(e){return e<=4?{level:"minimal",title:"轻微或无抑郁症状",description:"您目前的症状较轻微，建议继续保持良好的生活习惯。",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"}:e<=9?{level:"mild",title:"轻度抑郁症状",description:"您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}:e<=14?{level:"moderate",title:"中度抑郁症状",description:"您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"}:e<=19?{level:"moderately-severe",title:"中重度抑郁症状",description:"您的症状较为严重，强烈建议寻求专业的心理健康服务。",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}:{level:"severe",title:"重度抑郁症状",description:"您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",color:"text-red-800",bgColor:"bg-red-100",borderColor:"border-red-300"}}let d=[{id:"very-happy",label:"非常开心",emoji:"\uD83D\uDE04",color:"#10b981"},{id:"happy",label:"开心",emoji:"\uD83D\uDE0A",color:"#34d399"},{id:"neutral",label:"平静",emoji:"\uD83D\uDE10",color:"#6b7280"},{id:"sad",label:"难过",emoji:"\uD83D\uDE22",color:"#f59e0b"},{id:"very-sad",label:"非常难过",emoji:"\uD83D\uDE2D",color:"#ef4444"},{id:"anxious",label:"焦虑",emoji:"\uD83D\uDE30",color:"#8b5cf6"},{id:"angry",label:"愤怒",emoji:"\uD83D\uDE20",color:"#dc2626"},{id:"tired",label:"疲惫",emoji:"\uD83D\uDE34",color:"#64748b"}];function c(e){return e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}function m(){return Math.random().toString(36).substr(2,9)}let x={get:e=>{try{let r=localStorage.getItem(e);return r?JSON.parse(r):null}catch(e){return null}},set:(e,r)=>{try{localStorage.setItem(e,JSON.stringify(r))}catch(e){}},remove:e=>{try{localStorage.removeItem(e)}catch(e){}}}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>o});var s=t(95155),a=t(12115),l=t(59434);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});o.displayName="CardHeader";let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});n.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},71518:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(95155),a=t(12115),l=t(51934),i=t(69074),o=t(84616),n=t(72713),d=t(33109),c=t(30285),m=t(66695),x=t(59434),u=t(83540),h=t(56965),g=t(94754),b=t(96025),p=t(16238),f=t(94517),j=t(21374);function v(){var e,r;let[t,v]=(0,a.useState)([]),[y,N]=(0,a.useState)(!1),[w,C]=(0,a.useState)(""),[k,S]=(0,a.useState)("");(0,a.useEffect)(()=>{v((x.IG.get("moodEntries")||[]).map(e=>({...e,timestamp:new Date(e.timestamp)})))},[]);let D=e=>x._o.find(r=>r.id===e),I=t.slice(0,30).reverse().map(e=>{let r=D(e.moodId),t=3;switch(e.moodId){case"very-happy":t=5;break;case"happy":t=4;break;case"neutral":t=3;break;case"sad":case"anxious":case"angry":case"tired":t=2;break;case"very-sad":t=1}return{date:e.date,mood:t,label:(null==r?void 0:r.label)||"未知"}});return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-50",children:(0,s.jsxs)("div",{className:"mx-auto max-w-6xl px-6 py-8",children:[(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:(0,s.jsx)(m.Zp,{className:"mb-6",children:(0,s.jsxs)(m.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center",children:(0,s.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,s.jsx)(m.ZB,{className:"text-2xl",children:"情绪日志"}),(0,s.jsx)(m.BT,{children:"记录每日情绪变化，了解自己的情绪模式"})]})})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-1",children:[(0,s.jsxs)(m.Zp,{className:"mb-6",children:[(0,s.jsx)(m.aR,{children:(0,s.jsxs)(m.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"记录今日情绪"})]})}),(0,s.jsx)(m.Wu,{children:y?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择情绪"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2",children:x._o.map(e=>(0,s.jsxs)("button",{onClick:()=>C(e.id),className:"p-3 rounded-lg border-2 transition-all text-center ".concat(w===e.id?"border-green-500 bg-green-50":"border-gray-200 hover:border-green-300"),children:[(0,s.jsx)("div",{className:"text-2xl mb-1",children:e.emoji}),(0,s.jsx)("div",{className:"text-xs text-gray-600",children:e.label})]},e.id))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"备注 (可选)"}),(0,s.jsx)("textarea",{value:k,onChange:e=>S(e.target.value),placeholder:"今天发生了什么...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",rows:3})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(c.$,{onClick:()=>{if(!w)return;let e=[{id:(0,x.$C)(),date:new Date().toISOString().split("T")[0],moodId:w,note:k.trim(),timestamp:new Date},...t];v(e),x.IG.set("moodEntries",e),C(""),S(""),N(!1)},disabled:!w,className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700",children:"保存"}),(0,s.jsx)(c.$,{onClick:()=>{N(!1),C(""),S("")},variant:"outline",className:"flex-1",children:"取消"})]})]}):(0,s.jsx)(c.$,{onClick:()=>N(!0),className:"w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700",children:"添加情绪记录"})})]}),(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{children:(0,s.jsxs)(m.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"统计信息"})]})}),(0,s.jsx)(m.Wu,{children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"总记录数"}),(0,s.jsx)("span",{className:"font-semibold",children:t.length})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"本周记录"}),(0,s.jsx)("span",{className:"font-semibold",children:t.filter(e=>{let r=new Date(e.timestamp),t=new Date;return t.setDate(t.getDate()-7),r>=t}).length})]}),t.length>0&&(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"最近情绪"}),(0,s.jsxs)("span",{className:"font-semibold",children:[null==(e=D(t[0].moodId))?void 0:e.emoji," ",null==(r=D(t[0].moodId))?void 0:r.label]})]})]})})]})]}),(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[I.length>1&&(0,s.jsxs)(m.Zp,{children:[(0,s.jsxs)(m.aR,{children:[(0,s.jsxs)(m.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"情绪趋势"})]}),(0,s.jsx)(m.BT,{children:"最近30次记录的情绪变化趋势"})]}),(0,s.jsx)(m.Wu,{children:(0,s.jsx)("div",{className:"h-64",children:(0,s.jsx)(u.u,{width:"100%",height:"100%",children:(0,s.jsxs)(h.b,{data:I,children:[(0,s.jsx)(g.d,{strokeDasharray:"3 3"}),(0,s.jsx)(b.W,{dataKey:"date",tick:{fontSize:12},tickFormatter:e=>new Date(e).toLocaleDateString("zh-CN",{month:"short",day:"numeric"})}),(0,s.jsx)(p.h,{domain:[1,5],tick:{fontSize:12},tickFormatter:e=>["","低落","一般","平静","开心","很好"][e]||""}),(0,s.jsx)(f.m,{labelFormatter:e=>(0,x.Yq)(new Date(e)),formatter:(e,r,t)=>{var s;return[(null==t||null==(s=t.payload)?void 0:s.label)||"未知","情绪"]}}),(0,s.jsx)(j.N,{type:"monotone",dataKey:"mood",stroke:"#10b981",strokeWidth:2,dot:{fill:"#10b981",strokeWidth:2,r:4}})]})})})})]}),(0,s.jsxs)(m.Zp,{children:[(0,s.jsxs)(m.aR,{children:[(0,s.jsx)(m.ZB,{children:"最近记录"}),(0,s.jsx)(m.BT,{children:0===t.length?"还没有情绪记录":"共 ".concat(t.length," 条记录")})]}),(0,s.jsx)(m.Wu,{children:0===t.length?(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)(i.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),(0,s.jsx)("p",{children:"开始记录你的第一个情绪吧！"})]}):(0,s.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:t.map(e=>{let r=D(e.moodId);return(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl",children:null==r?void 0:r.emoji}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsx)("span",{className:"font-medium text-gray-900",children:null==r?void 0:r.label}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:(0,x.Yq)(e.timestamp)})]}),e.note&&(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.note})]})]},e.id)})})})]})]})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[778,934,319,441,684,358],()=>r(17976)),_N_E=e.O()}]);