# YYZ - 抑郁症研究与帮助

> 愿所有被困在夜里的灵魂，都能看到清晨的第一缕光

## 项目简介

YYZ是一个以"抑郁症研究与帮助"为核心的公益平台，旨在通过数字技术为抑郁症患者提供科普、评估、陪伴与援助服务。我们用科技的温度，为每一个需要帮助的人点亮希望之光。

## 核心功能

### 🧠 心理评估
- 基于PHQ-9量表的专业抑郁症评估工具
- 实时结果分析和建议
- 历史记录追踪

### 💬 AI陪聊
- 24小时温暖陪伴
- AI数字伙伴倾听心声
- 情感支持和理解

### 📅 情绪日志
- 每日情绪记录
- 数据可视化分析
- 情绪模式识别

### 📚 科普知识
- 权威的抑郁症科普内容
- 常见误区澄清
- 专业治疗信息

### 👥 加入我们
- 志愿者招募
- 多角色参与机会
- 公益协作平台

## 技术栈

- **前端框架**: Next.js 15 + React 18
- **样式**: TailwindCSS + Shadcn UI
- **动画**: Framer Motion
- **图表**: Recharts
- **图标**: Lucide React
- **类型检查**: TypeScript
- **部署**: Cloudflare Pages

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
```

### 导出静态文件

```bash
npm run export
```

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── assessment/         # 心理评估页面
│   ├── chat/              # AI陪聊页面
│   ├── education/         # 科普知识页面
│   ├── join/              # 加入我们页面
│   ├── mood/              # 情绪日志页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 组件
│   ├── ui/               # UI组件库
│   └── navigation.tsx    # 导航组件
└── lib/                  # 工具函数
    └── utils.ts          # 通用工具
```

## 部署

### Cloudflare Pages

1. 连接GitHub仓库到Cloudflare Pages
2. 设置构建命令: `npm run build`
3. 设置输出目录: `out`
4. 部署

### 环境变量

目前项目不需要额外的环境变量，所有功能都在前端实现。

## 贡献指南

我们欢迎各种形式的贡献：

- 🐛 报告Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🎨 UI/UX设计
- 💻 代码贡献

## 免责声明

⚠️ **重要提醒**: 本平台仅供参考和教育目的，不能替代专业医疗诊断和治疗。如果您或您身边的人正在经历严重的心理健康问题，请立即寻求专业医疗帮助。

## 联系我们

- 📧 邮箱: <EMAIL>
- 🌐 网站: [即将上线]

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

---

© 2024 YYZ Project. 让科技与善意同行，帮助每一个正在夜里独行的人。
