# 部署指南

## Cloudflare Pages 部署步骤

### 1. 准备工作

确保你已经有：
- Cloudflare 账户
- GitHub 账户
- 项目代码已推送到 GitHub

### 2. 连接 GitHub 仓库

1. 登录 Cloudflare Dashboard
2. 进入 Pages 部分
3. 点击 "Create a project"
4. 选择 "Connect to Git"
5. 授权 Cloudflare 访问你的 GitHub
6. 选择 YYZ 项目仓库

### 3. 配置构建设置

在 Cloudflare Pages 设置中：

- **Framework preset**: Next.js (Static HTML Export)
- **Build command**: `npm run build`
- **Build output directory**: `out`
- **Root directory**: `/` (如果项目在根目录)

### 4. 环境变量

目前项目不需要额外的环境变量。

### 5. 部署

点击 "Save and Deploy"，Cloudflare 将自动：
1. 克隆你的仓库
2. 安装依赖
3. 运行构建命令
4. 部署到全球 CDN

### 6. 自定义域名（可选）

部署完成后，你可以：
1. 在 Pages 项目设置中添加自定义域名
2. Cloudflare 会自动配置 SSL 证书

## 本地构建测试

在部署前，建议先在本地测试构建：

```bash
# 安装依赖
npm install

# 构建项目
npm run build

# 预览构建结果
npx serve out
```

## 故障排除

### 常见问题

1. **构建失败**
   - 检查 Node.js 版本（推荐 18+）
   - 确保所有依赖都已正确安装
   - 查看构建日志中的错误信息

2. **页面无法访问**
   - 确认 `next.config.ts` 中的 `output: 'export'` 配置
   - 检查路由是否正确

3. **样式丢失**
   - 确认 CSS 文件路径正确
   - 检查 TailwindCSS 配置

### 联系支持

如果遇到问题，可以：
- 查看 Cloudflare Pages 文档
- 联系项目维护者：<EMAIL>

## 更新部署

每次推送到主分支时，Cloudflare Pages 会自动重新部署。

你也可以在 Cloudflare Dashboard 中手动触发重新部署。
