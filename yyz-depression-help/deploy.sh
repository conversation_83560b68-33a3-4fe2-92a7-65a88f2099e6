#!/bin/bash

# YYZ 抑郁症研究与帮助 - 部署脚本
# 用于部署到 Cloudflare Pages

echo "🚀 开始部署 YYZ 项目到 Cloudflare Pages..."

# 检查是否安装了 wrangler
if ! command -v wrangler &> /dev/null; then
    echo "❌ 未找到 wrangler CLI，正在安装..."
    npm install -g wrangler
fi

# 构建项目
echo "📦 构建项目..."
npm run build

# 检查构建是否成功
if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
else
    echo "❌ 构建失败，请检查错误信息"
    exit 1
fi

# 部署到 Cloudflare Pages
echo "🌐 部署到 Cloudflare Pages..."
wrangler pages deploy out --project-name=yyz-depression-help

echo "🎉 部署完成！"
echo "🌐 您的应用现在可以在以下地址访问："
echo "   https://e79edfa4.yyz-depression-help.pages.dev"
echo "📊 Cloudflare Pages 控制台："
echo "   https://dash.cloudflare.com/pages"
echo "📧 部署邮箱: <EMAIL>"
