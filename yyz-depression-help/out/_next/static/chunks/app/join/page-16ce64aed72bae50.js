(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[335],{3601:(e,r,t)=>{Promise.resolve().then(t.bind(t,70150))},17580:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>n});var l=t(95155),a=t(12115),i=t(74466),s=t(59434);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",warm:"bg-gradient-to-r from-orange-400 to-pink-400 text-white hover:from-orange-500 hover:to-pink-500",calm:"bg-gradient-to-r from-blue-400 to-purple-400 text-white hover:from-blue-500 hover:to-purple-500"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,...n}=e;return(0,l.jsx)("button",{className:(0,s.cn)(o({variant:a,size:i,className:t})),ref:r,...n})});n.displayName="Button"},40646:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51976:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},59434:(e,r,t)=>{"use strict";t.d(r,{$C:()=>m,$Z:()=>s,C1:()=>o,IG:()=>u,Yq:()=>d,_o:()=>c,aX:()=>n,cn:()=>i});var l=t(52596),a=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,l.$)(r))}let s=[{id:1,question:"做事时提不起劲或没有兴趣",description:"在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"},{id:2,question:"感到心情低落、沮丧或绝望",description:"在过去两周内，您有多少天感到心情低落、沮丧或绝望？"},{id:3,question:"入睡困难、睡不安稳或睡眠过多",description:"在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"},{id:4,question:"感觉疲倦或没有活力",description:"在过去两周内，您有多少天感觉疲倦或没有活力？"},{id:5,question:"食欲不振或吃太多",description:"在过去两周内，您有多少天有食欲不振或吃太多的问题？"},{id:6,question:"觉得自己很糟糕或觉得自己很失败",description:"在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"},{id:7,question:"对事物专注有困难",description:"在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"},{id:8,question:"动作或说话速度缓慢或烦躁不安",description:"在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"},{id:9,question:"有不如死掉或伤害自己的念头",description:"在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"}],o=[{value:0,label:"完全没有",days:"0天"},{value:1,label:"几天",days:"1-6天"},{value:2,label:"一半以上的天数",days:"7-11天"},{value:3,label:"几乎每天",days:"12-14天"}];function n(e){return e<=4?{level:"minimal",title:"轻微或无抑郁症状",description:"您目前的症状较轻微，建议继续保持良好的生活习惯。",color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"}:e<=9?{level:"mild",title:"轻度抑郁症状",description:"您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"}:e<=14?{level:"moderate",title:"中度抑郁症状",description:"您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",color:"text-orange-600",bgColor:"bg-orange-50",borderColor:"border-orange-200"}:e<=19?{level:"moderately-severe",title:"中重度抑郁症状",description:"您的症状较为严重，强烈建议寻求专业的心理健康服务。",color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}:{level:"severe",title:"重度抑郁症状",description:"您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",color:"text-red-800",bgColor:"bg-red-100",borderColor:"border-red-300"}}let c=[{id:"very-happy",label:"非常开心",emoji:"\uD83D\uDE04",color:"#10b981"},{id:"happy",label:"开心",emoji:"\uD83D\uDE0A",color:"#34d399"},{id:"neutral",label:"平静",emoji:"\uD83D\uDE10",color:"#6b7280"},{id:"sad",label:"难过",emoji:"\uD83D\uDE22",color:"#f59e0b"},{id:"very-sad",label:"非常难过",emoji:"\uD83D\uDE2D",color:"#ef4444"},{id:"anxious",label:"焦虑",emoji:"\uD83D\uDE30",color:"#8b5cf6"},{id:"angry",label:"愤怒",emoji:"\uD83D\uDE20",color:"#dc2626"},{id:"tired",label:"疲惫",emoji:"\uD83D\uDE34",color:"#64748b"}];function d(e){return e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}function m(){return Math.random().toString(36).substr(2,9)}let u={get:e=>{try{let r=localStorage.getItem(e);return r?JSON.parse(r):null}catch(e){return null}},set:(e,r)=>{try{localStorage.setItem(e,JSON.stringify(r))}catch(e){}},remove:e=>{try{localStorage.removeItem(e)}catch(e){}}}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>n,Zp:()=>s,aR:()=>o});var l=t(95155),a=t(12115),i=t(59434);let s=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});s.displayName="Card";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...a})});o.displayName="CardHeader";let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});n.displayName="CardTitle";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",t),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",t),...a})});d.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,l.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},70150:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var l=t(95155),a=t(12115),i=t(51934),s=t(19946);let o=(0,s.A)("stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]),n=(0,s.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),c=(0,s.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),d=(0,s.A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]);var m=t(40646),u=t(17580),x=t(51976),p=t(90105),h=t(30285),f=t(66695);let b=[{icon:o,title:"心理专业支持者",description:"心理学/精神科背景，协助科普内容审核、AI对话引导",skills:["心理学专业","精神科医师","心理咨询师","社会工作者"],color:"from-blue-500 to-cyan-500"},{icon:n,title:"技术开发者",description:"前端/后端/AI开发，构建Web平台和AI陪聊系统",skills:["React/Next.js","Node.js/Python","AI/GPT API","数据库设计"],color:"from-green-500 to-emerald-500"},{icon:c,title:"设计师",description:"视觉设计/UI设计，为应用赋予温暖的视觉体验",skills:["UI/UX设计","品牌设计","插画设计","用户体验"],color:"from-purple-500 to-pink-500"},{icon:d,title:"内容创作者",description:"内容传播/写作/社媒运营，撰写科普文案和项目故事",skills:["内容写作","社媒运营","视频制作","科普传播"],color:"from-orange-500 to-red-500"}];function y(){let[e,r]=(0,a.useState)({name:"",email:"",role:"",skills:"",motivation:"",experience:""}),[t,s]=(0,a.useState)(!1),o=e=>{let{name:t,value:l}=e.target;r(e=>({...e,[t]:l}))};return t?(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center",children:(0,l.jsx)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8},className:"max-w-md mx-auto px-6",children:(0,l.jsxs)(f.Zp,{className:"text-center",children:[(0,l.jsxs)(f.aR,{children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(m.A,{className:"w-8 h-8 text-white"})}),(0,l.jsx)(f.ZB,{className:"text-2xl",children:"申请已提交"}),(0,l.jsx)(f.BT,{children:"感谢您的申请！我们会尽快与您联系。"})]}),(0,l.jsxs)(f.Wu,{children:[(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:"您的热情和专业技能对我们来说非常宝贵。我们期待与您一起为心理健康事业贡献力量。"}),(0,l.jsx)(h.$,{onClick:()=>{s(!1),r({name:"",email:"",role:"",skills:"",motivation:"",experience:""})},variant:"outline",className:"w-full",children:"提交另一个申请"})]})]})})}):(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50",children:(0,l.jsxs)("div",{className:"mx-auto max-w-6xl px-6 py-8",children:[(0,l.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:(0,l.jsx)(f.Zp,{className:"mb-8",children:(0,l.jsxs)(f.aR,{className:"text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(u.A,{className:"w-8 h-8 text-white"})}),(0,l.jsx)(f.ZB,{className:"text-3xl",children:"加入我们"}),(0,l.jsx)(f.BT,{className:"text-lg",children:"我们相信，一点善意能照亮一片黑暗"})]})})}),(0,l.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:(0,l.jsx)(f.Zp,{className:"mb-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,l.jsx)(f.Wu,{className:"pt-6",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)(x.A,{className:"w-12 h-12 mx-auto mb-4"}),(0,l.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"我们的使命"}),(0,l.jsx)("p",{className:"text-lg leading-relaxed max-w-3xl mx-auto",children:'YYZ 是一个以"抑郁症研究与帮助"为核心目标的公益性项目。我们希望以数字技术为桥梁， 让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声， 为困顿者搭建桥梁。'})]})})})}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,l.jsx)("div",{children:(0,l.jsxs)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"我们需要什么样的你？"}),(0,l.jsx)("div",{className:"space-y-4",children:b.map((e,r)=>{let t=e.icon;return(0,l.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*r},children:(0,l.jsx)(f.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,l.jsx)(f.Wu,{className:"pt-6",children:(0,l.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gradient-to-r ".concat(e.color," flex items-center justify-center flex-shrink-0"),children:(0,l.jsx)(t,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:e.title}),(0,l.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.description}),(0,l.jsx)("div",{className:"flex flex-wrap gap-1",children:e.skills.map(e=>(0,l.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:e},e))})]})]})})})},e.title)})})]})}),(0,l.jsx)("div",{children:(0,l.jsx)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.6},children:(0,l.jsxs)(f.Zp,{children:[(0,l.jsxs)(f.aR,{children:[(0,l.jsx)(f.ZB,{children:"志愿者申请表"}),(0,l.jsx)(f.BT,{children:"填写下方表单，让我们认识你！"})]}),(0,l.jsx)(f.Wu,{children:(0,l.jsxs)("form",{onSubmit:r=>{r.preventDefault(),console.log("Form submitted:",e),s(!0)},className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"姓名 *"}),(0,l.jsx)("input",{type:"text",name:"name",value:e.name,onChange:o,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的姓名"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"邮箱 *"}),(0,l.jsx)("input",{type:"email",name:"email",value:e.email,onChange:o,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"<EMAIL>"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"感兴趣的角色 *"}),(0,l.jsxs)("select",{name:"role",value:e.role,onChange:o,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,l.jsx)("option",{value:"",children:"请选择角色"}),b.map(e=>(0,l.jsx)("option",{value:e.title,children:e.title},e.title))]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"技能和经验"}),(0,l.jsx)("textarea",{name:"skills",value:e.skills,onChange:o,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请简述您的相关技能和经验..."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"参与动机 *"}),(0,l.jsx)("textarea",{name:"motivation",value:e.motivation,onChange:o,required:!0,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"为什么想要加入我们？您希望为这个项目贡献什么？"})]}),(0,l.jsxs)(h.$,{type:"submit",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:[(0,l.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"提交申请"]})]})})]})})})]}),(0,l.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"mt-12",children:(0,l.jsx)(f.Zp,{className:"bg-gray-50",children:(0,l.jsx)(f.Wu,{className:"pt-6",children:(0,l.jsxs)("blockquote",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-xl italic text-gray-700 mb-4",children:'"                  "如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。""'}),(0,l.jsx)("footer",{className:"text-gray-500",children:"— YYZ 项目发起人"})]})})})})]})})}},90105:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});let l=(0,t(19946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[778,934,441,684,358],()=>r(3601)),_N_E=e.O()}]);