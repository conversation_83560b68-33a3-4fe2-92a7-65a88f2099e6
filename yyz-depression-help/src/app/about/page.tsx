'use client'

import { motion } from 'framer-motion'
import { Heart, Target, Users, Lightbulb, FileText, Map, Calendar, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const milestones = [
  {
    phase: '阶段一',
    time: '0~3个月',
    title: 'Web Demo开发',
    description: '项目介绍站点+AI陪聊+评估工具原型',
    status: 'completed'
  },
  {
    phase: '阶段二',
    time: '3~6个月',
    title: '用户测试与团队建设',
    description: '完善功能模块，建立专业志愿团队和专家顾问组',
    status: 'current'
  },
  {
    phase: '阶段三',
    time: '6~12个月',
    title: 'App原型开发',
    description: '构建App原型，优化数据结构，跨平台同步',
    status: 'planned'
  },
  {
    phase: '阶段四',
    time: '1~2年',
    title: '全国支持网络',
    description: '建立全国范围内支持网络，推动与政策协同与高校试点',
    status: 'planned'
  }
]

const values = [
  {
    icon: Target,
    title: '早识别',
    description: '使用评估工具、AI陪聊、情绪日志，帮助用户早发现早介入'
  },
  {
    icon: Heart,
    title: '易使用',
    description: '平台界面简洁温和、可匿名、无登录体验核心功能'
  },
  {
    icon: Users,
    title: '多维度',
    description: '兼顾医学、心理、社会工作视角，构建支持生态'
  },
  {
    icon: Lightbulb,
    title: '真共情',
    description: '数字人陪伴+人类志愿者支援，打造可信赖的温度'
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="mx-auto max-w-6xl px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Card className="mb-8">
            <CardHeader className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Heart className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-3xl">关于我们</CardTitle>
              <CardDescription className="text-lg">
                了解YYZ项目的初衷、愿景与发展规划
              </CardDescription>
            </CardHeader>
          </Card>
        </motion.div>

        {/* Mission Statement */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="pt-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold mb-4">项目愿景</h2>
                <p className="text-lg leading-relaxed max-w-4xl mx-auto mb-6">
                  构建一个以人为本、科技赋能的数字心理健康生态，帮助抑郁症患者"认识自己、倾诉情绪、获得治疗、重回生活"。
                </p>
                <blockquote className="text-xl italic border-l-4 border-white pl-4 text-blue-100">
                  "愿所有被困在夜里的灵魂，都能看到清晨的第一缕光。"
                </blockquote>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Core Values */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">核心价值主张</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => {
              const Icon = value.icon
              return (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                >
                  <Card className="h-full text-center hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <CardTitle className="text-lg">{value.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 text-sm">{value.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* Roadmap */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-8"
        >
          <Card>
            <CardHeader className="text-center">
              <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                <Map className="w-6 h-6 text-white" />
              </div>
              <CardTitle className="text-2xl">发展路线图</CardTitle>
              <CardDescription>
                阶段性开发计划与里程碑
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {milestones.map((milestone, index) => (
                  <motion.div
                    key={milestone.phase}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 * index }}
                    className="flex items-start space-x-4"
                  >
                    <div className={`w-4 h-4 rounded-full mt-2 flex-shrink-0 ${
                      milestone.status === 'completed' ? 'bg-green-500' :
                      milestone.status === 'current' ? 'bg-blue-500' :
                      'bg-gray-300'
                    }`}></div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-semibold text-gray-900">{milestone.phase}</h3>
                        <span className="text-sm text-gray-500">({milestone.time})</span>
                        {milestone.status === 'current' && (
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                            进行中
                          </span>
                        )}
                        {milestone.status === 'completed' && (
                          <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            已完成
                          </span>
                        )}
                      </div>
                      <h4 className="font-medium text-gray-800 mb-1">{milestone.title}</h4>
                      <p className="text-gray-600 text-sm">{milestone.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Links */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">了解更多</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <FileText className="w-8 h-8 text-blue-600" />
                  <div>
                    <CardTitle className="group-hover:text-blue-600 transition-colors">
                      项目初衷
                    </CardTitle>
                    <CardDescription>
                      查看完整的产品需求与开发文档
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  深入了解YYZ项目的技术架构、功能模块设计和开发计划。
                </p>
                <Button variant="outline" size="sm" className="group-hover:border-blue-500 group-hover:text-blue-600">
                  查看文档 <ArrowRight className="w-3 h-3 ml-1" />
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <Calendar className="w-8 h-8 text-purple-600" />
                  <div>
                    <CardTitle className="group-hover:text-purple-600 transition-colors">
                      白皮书
                    </CardTitle>
                    <CardDescription>
                      项目背景、愿景与社会价值
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  了解项目的社会背景、核心价值主张和未来发展愿景。
                </p>
                <Button variant="outline" size="sm" className="group-hover:border-purple-500 group-hover:text-purple-600">
                  查看白皮书 <ArrowRight className="w-3 h-3 ml-1" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <Card className="bg-gradient-to-r from-orange-500 to-pink-600 text-white">
            <CardContent className="pt-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold mb-4">加入我们的使命</h2>
                <p className="text-lg mb-6 text-orange-100">
                  如果你也相信科技可以传递温暖，愿意为心理健康事业贡献力量
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" variant="secondary" asChild>
                    <Link href="/join" className="flex items-center space-x-2">
                      <Users className="w-5 h-5" />
                      <span>成为志愿者</span>
                    </Link>
                  </Button>
                  <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-orange-600" asChild>
                    <Link href="/assessment">开始体验</Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
