'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Brain, ArrowLeft, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { PHQ9_QUESTIONS, PHQ9_OPTIONS, interpretPHQ9Score } from '@/lib/utils'

export default function AssessmentPage() {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<number[]>(new Array(PHQ9_QUESTIONS.length).fill(-1))
  const [, setIsCompleted] = useState(false)
  const [showResult, setShowResult] = useState(false)

  const progress = ((currentQuestion + 1) / PHQ9_QUESTIONS.length) * 100
  const totalScore = answers.reduce((sum, answer) => sum + (answer > -1 ? answer : 0), 0)
  const result = interpretPHQ9Score(totalScore)

  const handleAnswer = (value: number) => {
    const newAnswers = [...answers]
    newAnswers[currentQuestion] = value
    setAnswers(newAnswers)

    if (currentQuestion < PHQ9_QUESTIONS.length - 1) {
      setTimeout(() => {
        setCurrentQuestion(currentQuestion + 1)
      }, 300)
    } else {
      setIsCompleted(true)
      setTimeout(() => {
        setShowResult(true)
      }, 500)
    }
  }

  const goToPrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const resetAssessment = () => {
    setCurrentQuestion(0)
    setAnswers(new Array(PHQ9_QUESTIONS.length).fill(-1))
    setIsCompleted(false)
    setShowResult(false)
  }

  if (showResult) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-12">
        <div className="mx-auto max-w-4xl px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Card className="mb-8">
              <CardHeader className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl">评估完成</CardTitle>
                <CardDescription>
                  感谢您完成PHQ-9抑郁症评估量表
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className={`mb-8 border-2 ${result.borderColor}`}>
              <CardHeader className={`${result.bgColor} rounded-t-lg`}>
                <CardTitle className={`text-xl ${result.color}`}>
                  评估结果：{result.title}
                </CardTitle>
                <div className="text-3xl font-bold text-gray-900">
                  总分：{totalScore}/27
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <p className="text-gray-700 mb-6">{result.description}</p>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold text-yellow-800 mb-2">重要提醒</h4>
                  <p className="text-yellow-700 text-sm">
                    本评估仅供参考，不能替代专业医疗诊断。如果您感到困扰或有自伤想法，请立即寻求专业帮助。
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button onClick={resetAssessment} variant="outline" className="w-full">
                    重新评估
                  </Button>
                  <Button variant="warm" className="w-full">
                    寻求帮助
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>您的答案回顾</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {PHQ9_QUESTIONS.map((question, index) => (
                    <div key={question.id} className="flex justify-between items-center py-2 border-b border-gray-100">
                      <span className="text-sm text-gray-600 flex-1">
                        {question.question}
                      </span>
                      <span className="text-sm font-medium text-gray-900 ml-4">
                        {PHQ9_OPTIONS[answers[index]]?.label || '未回答'}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Header */}
          <Card className="mb-8">
            <CardHeader className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-2xl">PHQ-9 抑郁症评估</CardTitle>
              <CardDescription>
                请根据过去两周的感受，选择最符合您情况的选项
              </CardDescription>
            </CardHeader>
          </Card>

          {/* Progress */}
          <Card className="mb-8">
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">
                  问题 {currentQuestion + 1} / {PHQ9_QUESTIONS.length}
                </span>
                <span className="text-sm text-gray-600">
                  {Math.round(progress)}% 完成
                </span>
              </div>
              <Progress value={progress} className="h-2" />
            </CardContent>
          </Card>

          {/* Question */}
          <motion.div
            key={currentQuestion}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="text-xl">
                  {PHQ9_QUESTIONS[currentQuestion].question}
                </CardTitle>
                <CardDescription>
                  {PHQ9_QUESTIONS[currentQuestion].description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {PHQ9_OPTIONS.map((option) => (
                    <motion.button
                      key={option.value}
                      onClick={() => handleAnswer(option.value)}
                      className={`w-full p-4 text-left rounded-lg border-2 transition-all hover:border-blue-300 hover:bg-blue-50 ${
                        answers[currentQuestion] === option.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 bg-white'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium text-gray-900">{option.label}</div>
                          <div className="text-sm text-gray-600">{option.days}</div>
                        </div>
                        <div className="text-2xl font-bold text-blue-600">{option.value}</div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Navigation */}
          <div className="flex justify-between">
            <Button
              onClick={goToPrevious}
              variant="outline"
              disabled={currentQuestion === 0}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>上一题</span>
            </Button>
            
            <div className="text-sm text-gray-500 flex items-center">
              点击选项自动进入下一题
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
