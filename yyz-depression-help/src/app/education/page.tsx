'use client'

import { motion } from 'framer-motion'
import { BookOpen, Heart, Brain, AlertCircle, CheckCircle, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const articles = [
  {
    id: 1,
    title: '什么是抑郁症？',
    description: '了解抑郁症的基本概念、症状表现和常见误区',
    category: '基础知识',
    readTime: '5分钟',
    color: 'from-blue-500 to-cyan-500',
    content: `
      抑郁症是一种常见的心理疾病，影响着全球超过3.5亿人。它不仅仅是"心情不好"，
      而是一种持续的、严重影响日常生活的情绪障碍。

      主要症状包括：
      • 持续的悲伤或空虚感
      • 对平时喜欢的活动失去兴趣
      • 疲劳和精力不足
      • 睡眠问题
      • 食欲变化
      • 注意力难以集中
      • 自我价值感低
      • 有时会有自伤想法

      重要的是要知道，抑郁症是可以治疗的，寻求帮助是勇敢的表现。
    `
  },
  {
    id: 2,
    title: '如何识别抑郁症的早期信号',
    description: '学会识别自己和他人的抑郁症早期症状',
    category: '识别诊断',
    readTime: '7分钟',
    color: 'from-green-500 to-emerald-500',
    content: `
      早期识别抑郁症对于及时干预非常重要。以下是一些需要关注的信号：

      情绪变化：
      • 持续两周以上的低落情绪
      • 对未来感到绝望
      • 易怒或焦虑
      • 情绪波动大

      行为变化：
      • 社交退缩，避免与人接触
      • 工作或学习效率下降
      • 个人卫生习惯改变
      • 睡眠模式改变

      身体症状：
      • 不明原因的疲劳
      • 头痛或身体疼痛
      • 消化问题
      • 食欲变化

      如果这些症状持续存在并影响日常生活，建议寻求专业帮助。
    `
  },
  {
    id: 3,
    title: '抑郁症的治疗方法',
    description: '了解现代医学对抑郁症的治疗方案',
    category: '治疗康复',
    readTime: '8分钟',
    color: 'from-purple-500 to-pink-500',
    content: `
      抑郁症有多种有效的治疗方法，通常需要个性化的治疗方案：

      心理治疗：
      • 认知行为疗法(CBT)
      • 人际关系疗法
      • 正念疗法
      • 家庭治疗

      药物治疗：
      • 抗抑郁药物
      • 需要专业医生指导
      • 通常需要4-6周见效
      • 不要自行停药

      生活方式调整：
      • 规律运动
      • 健康饮食
      • 充足睡眠
      • 社交支持
      • 压力管理

      综合治疗效果最佳，重要的是要有耐心，康复是一个过程。
    `
  },
  {
    id: 4,
    title: '如何帮助身边的抑郁症患者',
    description: '学习如何为抑郁症患者提供支持和帮助',
    category: '支持他人',
    readTime: '6分钟',
    color: 'from-orange-500 to-red-500',
    content: `
      当身边有人患抑郁症时，你的支持非常重要：

      倾听和理解：
      • 耐心倾听，不要急于给建议
      • 避免说"想开点"这样的话
      • 承认他们的感受是真实的
      • 不要试图"修复"他们

      实际支持：
      • 陪伴就医
      • 帮助日常事务
      • 保持定期联系
      • 鼓励专业治疗

      照顾自己：
      • 设定界限
      • 寻求支持
      • 了解抑郁症知识
      • 保持自己的心理健康

      记住，你不能"治愈"别人的抑郁症，但你的支持和陪伴是珍贵的。
    `
  }
]

const myths = [
  {
    myth: '抑郁症就是想太多',
    fact: '抑郁症是一种真实的医学疾病，涉及大脑化学物质的变化'
  },
  {
    myth: '抑郁症患者都很脆弱',
    fact: '抑郁症可以影响任何人，包括非常坚强的人'
  },
  {
    myth: '抑郁症会自己好起来',
    fact: '抑郁症需要专业治疗，不会自动消失'
  },
  {
    myth: '药物治疗会让人上瘾',
    fact: '抗抑郁药物不会成瘾，但需要在医生指导下使用'
  }
]

export default function EducationPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <div className="mx-auto max-w-6xl px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Card className="mb-8">
            <CardHeader className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-3xl">科普知识</CardTitle>
              <CardDescription className="text-lg">
                权威的抑郁症科普内容，帮助您正确认识和理解心理健康
              </CardDescription>
            </CardHeader>
          </Card>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-orange-600 to-red-600 text-white">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <div className="text-3xl font-bold mb-2">3.5亿+</div>
                  <div className="text-orange-100">全球抑郁症患者</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">1/4</div>
                  <div className="text-orange-100">女性患病率</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">90%</div>
                  <div className="text-orange-100">治疗有效率</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Articles */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">科普文章</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {articles.map((article, index) => (
              <motion.div
                key={article.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r ${article.color}`}>
                        {article.category}
                      </span>
                      <span className="text-sm text-gray-500">{article.readTime}</span>
                    </div>
                    <CardTitle className="group-hover:text-orange-600 transition-colors">
                      {article.title}
                    </CardTitle>
                    <CardDescription>{article.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm text-gray-600 mb-4 line-clamp-3">
                      {article.content.substring(0, 150)}...
                    </div>
                    <Button variant="outline" size="sm" className="group-hover:border-orange-500 group-hover:text-orange-600">
                      阅读更多 <ArrowRight className="w-3 h-3 ml-1" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Myths vs Facts */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-6">常见误区澄清</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {myths.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
              >
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-red-700 mb-1">误区</h4>
                          <p className="text-sm text-gray-600">{item.myth}</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-green-700 mb-1">事实</h4>
                          <p className="text-sm text-gray-600">{item.fact}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="pt-6">
              <div className="text-center">
                <Heart className="w-12 h-12 mx-auto mb-4" />
                <h2 className="text-2xl font-bold mb-4">需要帮助？</h2>
                <p className="text-lg mb-6 text-blue-100">
                  如果您或您身边的人正在经历抑郁症状，请不要犹豫寻求专业帮助
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" variant="secondary" asChild>
                    <Link href="/assessment" className="flex items-center space-x-2">
                      <Brain className="w-5 h-5" />
                      <span>开始评估</span>
                    </Link>
                  </Button>
                  <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600" asChild>
                    <Link href="/chat">AI陪聊</Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
