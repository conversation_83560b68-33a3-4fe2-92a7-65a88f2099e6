'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Users, Heart, Code, Palette, PenTool, Stethoscope, Send, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const roles = [
  {
    icon: Stethoscope,
    title: '心理专业支持者',
    description: '心理学/精神科背景，协助科普内容审核、AI对话引导',
    skills: ['心理学专业', '精神科医师', '心理咨询师', '社会工作者'],
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: Code,
    title: '技术开发者',
    description: '前端/后端/AI开发，构建Web平台和AI陪聊系统',
    skills: ['React/Next.js', 'Node.js/Python', 'AI/GPT API', '数据库设计'],
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: Palette,
    title: '设计师',
    description: '视觉设计/UI设计，为应用赋予温暖的视觉体验',
    skills: ['UI/UX设计', '品牌设计', '插画设计', '用户体验'],
    color: 'from-purple-500 to-pink-500'
  },
  {
    icon: PenTool,
    title: '内容创作者',
    description: '内容传播/写作/社媒运营，撰写科普文案和项目故事',
    skills: ['内容写作', '社媒运营', '视频制作', '科普传播'],
    color: 'from-orange-500 to-red-500'
  }
]

export default function JoinPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: '',
    skills: '',
    motivation: '',
    experience: ''
  })
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically send the data to your backend
    console.log('Form submitted:', formData)
    setIsSubmitted(true)
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          className="max-w-md mx-auto px-6"
        >
          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-2xl">申请已提交</CardTitle>
              <CardDescription>
                感谢您的申请！我们会尽快与您联系。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-6">
                您的热情和专业技能对我们来说非常宝贵。我们期待与您一起为心理健康事业贡献力量。
              </p>
              <Button 
                onClick={() => {
                  setIsSubmitted(false)
                  setFormData({
                    name: '',
                    email: '',
                    role: '',
                    skills: '',
                    motivation: '',
                    experience: ''
                  })
                }}
                variant="outline"
                className="w-full"
              >
                提交另一个申请
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="mx-auto max-w-6xl px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Card className="mb-8">
            <CardHeader className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Users className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-3xl">加入我们</CardTitle>
              <CardDescription className="text-lg">
                我们相信，一点善意能照亮一片黑暗
              </CardDescription>
            </CardHeader>
          </Card>
        </motion.div>

        {/* Mission Statement */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <Card className="mb-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="pt-6">
              <div className="text-center">
                <Heart className="w-12 h-12 mx-auto mb-4" />
                <h2 className="text-2xl font-bold mb-4">我们的使命</h2>
                <p className="text-lg leading-relaxed max-w-3xl mx-auto">
                  YYZ 是一个以&quot;抑郁症研究与帮助&quot;为核心目标的公益性项目。我们希望以数字技术为桥梁，
                  让心理健康更可见、更被理解、更有支持。在这个万物喧嚣的世界，我们愿意为沉默的人发声，
                  为困顿者搭建桥梁。
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Roles */}
          <div>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">我们需要什么样的你？</h2>
              <div className="space-y-4">
                {roles.map((role, index) => {
                  const Icon = role.icon
                  return (
                    <motion.div
                      key={role.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.1 * index }}
                    >
                      <Card className="hover:shadow-lg transition-shadow">
                        <CardContent className="pt-6">
                          <div className="flex items-start space-x-4">
                            <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${role.color} flex items-center justify-center flex-shrink-0`}>
                              <Icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900 mb-2">{role.title}</h3>
                              <p className="text-gray-600 text-sm mb-3">{role.description}</p>
                              <div className="flex flex-wrap gap-1">
                                {role.skills.map((skill) => (
                                  <span
                                    key={skill}
                                    className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                                  >
                                    {skill}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </div>
            </motion.div>
          </div>

          {/* Application Form */}
          <div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>志愿者申请表</CardTitle>
                  <CardDescription>
                    填写下方表单，让我们认识你！
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        姓名 *
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="请输入您的姓名"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        邮箱 *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        感兴趣的角色 *
                      </label>
                      <select
                        name="role"
                        value={formData.role}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">请选择角色</option>
                        {roles.map((role) => (
                          <option key={role.title} value={role.title}>
                            {role.title}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        技能和经验
                      </label>
                      <textarea
                        name="skills"
                        value={formData.skills}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="请简述您的相关技能和经验..."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        参与动机 *
                      </label>
                      <textarea
                        name="motivation"
                        value={formData.motivation}
                        onChange={handleInputChange}
                        required
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="为什么想要加入我们？您希望为这个项目贡献什么？"
                      />
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    >
                      <Send className="w-4 h-4 mr-2" />
                      提交申请
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>

        {/* Quote */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-12"
        >
          <Card className="bg-gray-50">
            <CardContent className="pt-6">
              <blockquote className="text-center">
                <p className="text-xl italic text-gray-700 mb-4">
                  "                  &quot;如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。&quot;"
                </p>
                <footer className="text-gray-500">
                  — YYZ 项目发起人
                </footer>
              </blockquote>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
