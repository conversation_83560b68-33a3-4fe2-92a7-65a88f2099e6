import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/navigation";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "YYZ - 抑郁症研究与帮助",
  description: "YYZ是一个以抑郁症研究与帮助为核心的公益平台，提供心理评估、AI陪聊、情绪日志等功能，为每一个需要帮助的人点亮希望之光。",
  keywords: "抑郁症,心理健康,心理评估,AI陪聊,情绪日志,公益平台",
  authors: [{ name: "YYZ Team" }],
  openGraph: {
    title: "YYZ - 抑郁症研究与帮助",
    description: "愿所有被困在夜里的灵魂，都能看到清晨的第一缕光",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className={`${inter.variable} font-sans antialiased`}>
        <Navigation />
        <main>{children}</main>
      </body>
    </html>
  );
}
