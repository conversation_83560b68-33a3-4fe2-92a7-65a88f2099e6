'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { FileText, Download, Pill, User, Heart, Shield, AlertCircle, Plus, Edit3 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface MedicalRecord {
  id: string
  type: 'assessment' | 'medication' | 'consultation'
  date: string
  title: string
  content: string
  score?: number
}

const sampleRecords: MedicalRecord[] = [
  {
    id: '1',
    type: 'assessment',
    date: '2024-06-05',
    title: 'PHQ-9 抑郁症评估',
    content: '总分：12分，中度抑郁症状',
    score: 12
  },
  {
    id: '2',
    type: 'medication',
    date: '2024-06-03',
    title: '用药记录',
    content: '舍曲林 50mg，每日一次，晨服'
  },
  {
    id: '3',
    type: 'consultation',
    date: '2024-06-01',
    title: '心理咨询',
    content: '认知行为疗法，第3次会谈，情绪有所改善'
  }
]

export default function MedicalPage() {
  const [records] = useState<MedicalRecord[]>(sampleRecords)
  const [showAddForm, setShowAddForm] = useState(false)
  const [recordType, setRecordType] = useState<'medication' | 'consultation'>('medication')

  const getRecordIcon = (type: string) => {
    switch (type) {
      case 'assessment': return FileText
      case 'medication': return Pill
      case 'consultation': return User
      default: return FileText
    }
  }

  const getRecordColor = (type: string) => {
    switch (type) {
      case 'assessment': return 'from-blue-500 to-cyan-500'
      case 'medication': return 'from-green-500 to-emerald-500'
      case 'consultation': return 'from-purple-500 to-pink-500'
      default: return 'from-gray-500 to-gray-600'
    }
  }

  const generatePDF = () => {
    // 模拟PDF生成
    alert('PDF报告生成中...（Demo版本）')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      <div className="mx-auto max-w-6xl px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Card className="mb-8">
            <CardHeader className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center">
                <FileText className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-2xl">医疗数据与导出</CardTitle>
              <CardDescription>
                管理您的健康记录，生成专业医疗报告
              </CardDescription>
            </CardHeader>
          </Card>
        </motion.div>

        {/* Privacy Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <Shield className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-blue-800 mb-1">隐私保护</h4>
                  <p className="text-sm text-blue-700">
                    您的所有医疗数据都存储在本地设备中，我们不会上传或分享您的个人健康信息。
                    导出的PDF报告仅供您个人使用或与医生分享。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Records List */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">健康记录</h2>
                <Button
                  onClick={() => setShowAddForm(!showAddForm)}
                  className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  添加记录
                </Button>
              </div>

              {/* Add Record Form */}
              {showAddForm && (
                <Card className="mb-6">
                  <CardHeader>
                    <CardTitle className="text-lg">添加新记录</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          记录类型
                        </label>
                        <div className="flex space-x-4">
                          <label className="flex items-center">
                            <input
                              type="radio"
                              value="medication"
                              checked={recordType === 'medication'}
                              onChange={(e) => setRecordType(e.target.value as 'medication')}
                              className="mr-2"
                            />
                            用药记录
                          </label>
                          <label className="flex items-center">
                            <input
                              type="radio"
                              value="consultation"
                              checked={recordType === 'consultation'}
                              onChange={(e) => setRecordType(e.target.value as 'consultation')}
                              className="mr-2"
                            />
                            咨询记录
                          </label>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          日期
                        </label>
                        <input
                          type="date"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          defaultValue={new Date().toISOString().split('T')[0]}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {recordType === 'medication' ? '药物名称和剂量' : '咨询内容'}
                        </label>
                        <textarea
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder={recordType === 'medication' ? '例如：舍曲林 50mg，每日一次' : '例如：认知行为疗法，第1次会谈'}
                        />
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          onClick={() => {
                            alert('记录已保存（Demo版本）')
                            setShowAddForm(false)
                          }}
                          className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700"
                        >
                          保存记录
                        </Button>
                        <Button
                          onClick={() => setShowAddForm(false)}
                          variant="outline"
                        >
                          取消
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Records */}
              <div className="space-y-4">
                {records.map((record, index) => {
                  const Icon = getRecordIcon(record.type)
                  return (
                    <motion.div
                      key={record.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.1 * index }}
                    >
                      <Card className="hover:shadow-lg transition-shadow">
                        <CardContent className="pt-6">
                          <div className="flex items-start space-x-4">
                            <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${getRecordColor(record.type)} flex items-center justify-center flex-shrink-0`}>
                              <Icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h3 className="font-semibold text-gray-900 mb-1">{record.title}</h3>
                                  <p className="text-gray-600 text-sm mb-2">{record.content}</p>
                                  <p className="text-gray-500 text-xs">{record.date}</p>
                                </div>
                                <Button variant="ghost" size="sm">
                                  <Edit3 className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </div>
            </motion.div>
          </div>

          {/* Export Panel */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="space-y-6"
            >
              {/* Export Options */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Download className="w-5 h-5" />
                    <span>导出报告</span>
                  </CardTitle>
                  <CardDescription>
                    生成专业的健康概览报告
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Button
                      onClick={generatePDF}
                      className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      生成PDF报告
                    </Button>
                    
                    <div className="text-sm text-gray-600">
                      <p className="mb-2">报告将包含：</p>
                      <ul className="space-y-1 text-xs">
                        <li>• 评估结果趋势图</li>
                        <li>• 用药记录时间线</li>
                        <li>• 咨询会谈总结</li>
                        <li>• 情绪变化分析</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Heart className="w-5 h-5" />
                    <span>健康统计</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">评估次数</span>
                      <span className="font-semibold">1</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">用药记录</span>
                      <span className="font-semibold">1</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">咨询次数</span>
                      <span className="font-semibold">1</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">最近评估</span>
                      <span className="font-semibold text-orange-600">中度</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Disclaimer */}
              <Card className="bg-yellow-50 border-yellow-200">
                <CardContent className="pt-6">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-yellow-800 mb-1">重要提醒</h4>
                      <p className="text-sm text-yellow-700">
                        本功能生成的报告仅供参考，不能替代专业医疗诊断。
                        请将报告作为与医生沟通的辅助工具。
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
