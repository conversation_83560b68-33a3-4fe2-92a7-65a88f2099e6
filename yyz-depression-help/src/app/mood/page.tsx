'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Calendar, Plus, TrendingUp, BarChart3 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MOOD_TYPES, formatDate, storage, generateId } from '@/lib/utils'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

interface MoodEntry {
  id: string
  date: string
  moodId: string
  note: string
  timestamp: Date
}

export default function MoodPage() {
  const [entries, setEntries] = useState<MoodEntry[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [selectedMood, setSelectedMood] = useState('')
  const [note, setNote] = useState('')

  useEffect(() => {
    const savedEntries = storage.get('moodEntries') || []
    setEntries(savedEntries.map((entry: MoodEntry) => ({
      ...entry,
      timestamp: new Date(entry.timestamp)
    })))
  }, [])

  const saveMoodEntry = () => {
    if (!selectedMood) return

    const newEntry: MoodEntry = {
      id: generateId(),
      date: new Date().toISOString().split('T')[0],
      moodId: selectedMood,
      note: note.trim(),
      timestamp: new Date()
    }

    const updatedEntries = [newEntry, ...entries]
    setEntries(updatedEntries)
    storage.set('moodEntries', updatedEntries)

    // Reset form
    setSelectedMood('')
    setNote('')
    setShowAddForm(false)
  }

  const getMoodById = (id: string) => {
    return MOOD_TYPES.find(mood => mood.id === id)
  }

  // Prepare chart data
  const chartData = entries
    .slice(0, 30) // Last 30 entries
    .reverse()
    .map((entry) => {
      const mood = getMoodById(entry.moodId)
      let moodValue = 3 // neutral default
      
      switch (entry.moodId) {
        case 'very-happy': moodValue = 5; break
        case 'happy': moodValue = 4; break
        case 'neutral': moodValue = 3; break
        case 'sad': moodValue = 2; break
        case 'very-sad': moodValue = 1; break
        case 'anxious': moodValue = 2; break
        case 'angry': moodValue = 2; break
        case 'tired': moodValue = 2; break
      }

      return {
        date: entry.date,
        mood: moodValue,
        label: mood?.label || '未知'
      }
    })

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-50">
      <div className="mx-auto max-w-6xl px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Card className="mb-6">
            <CardHeader className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                <Calendar className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-2xl">情绪日志</CardTitle>
              <CardDescription>
                记录每日情绪变化，了解自己的情绪模式
              </CardDescription>
            </CardHeader>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Add Mood Entry */}
          <div className="lg:col-span-1">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Plus className="w-5 h-5" />
                  <span>记录今日情绪</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!showAddForm ? (
                  <Button 
                    onClick={() => setShowAddForm(true)}
                    className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                  >
                    添加情绪记录
                  </Button>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        选择情绪
                      </label>
                      <div className="grid grid-cols-2 gap-2">
                        {MOOD_TYPES.map((mood) => (
                          <button
                            key={mood.id}
                            onClick={() => setSelectedMood(mood.id)}
                            className={`p-3 rounded-lg border-2 transition-all text-center ${
                              selectedMood === mood.id
                                ? 'border-green-500 bg-green-50'
                                : 'border-gray-200 hover:border-green-300'
                            }`}
                          >
                            <div className="text-2xl mb-1">{mood.emoji}</div>
                            <div className="text-xs text-gray-600">{mood.label}</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        备注 (可选)
                      </label>
                      <textarea
                        value={note}
                        onChange={(e) => setNote(e.target.value)}
                        placeholder="今天发生了什么..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        rows={3}
                      />
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        onClick={saveMoodEntry}
                        disabled={!selectedMood}
                        className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                      >
                        保存
                      </Button>
                      <Button
                        onClick={() => {
                          setShowAddForm(false)
                          setSelectedMood('')
                          setNote('')
                        }}
                        variant="outline"
                        className="flex-1"
                      >
                        取消
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5" />
                  <span>统计信息</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">总记录数</span>
                    <span className="font-semibold">{entries.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">本周记录</span>
                    <span className="font-semibold">
                      {entries.filter(entry => {
                        const entryDate = new Date(entry.timestamp)
                        const weekAgo = new Date()
                        weekAgo.setDate(weekAgo.getDate() - 7)
                        return entryDate >= weekAgo
                      }).length}
                    </span>
                  </div>
                  {entries.length > 0 && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">最近情绪</span>
                      <span className="font-semibold">
                        {getMoodById(entries[0].moodId)?.emoji} {getMoodById(entries[0].moodId)?.label}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Chart and History */}
          <div className="lg:col-span-2 space-y-6">
            {/* Mood Trend Chart */}
            {chartData.length > 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5" />
                    <span>情绪趋势</span>
                  </CardTitle>
                  <CardDescription>
                    最近30次记录的情绪变化趋势
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="date" 
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                        />
                        <YAxis 
                          domain={[1, 5]}
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => {
                            const labels = ['', '低落', '一般', '平静', '开心', '很好']
                            return labels[value] || ''
                          }}
                        />
                        <Tooltip
                          labelFormatter={(value) => formatDate(new Date(value))}
                          formatter={(value, name, props) => [props?.payload?.label || '未知', '情绪']}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="mood" 
                          stroke="#10b981" 
                          strokeWidth={2}
                          dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Recent Entries */}
            <Card>
              <CardHeader>
                <CardTitle>最近记录</CardTitle>
                <CardDescription>
                  {entries.length === 0 ? '还没有情绪记录' : `共 ${entries.length} 条记录`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {entries.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>开始记录你的第一个情绪吧！</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {entries.map((entry) => {
                      const mood = getMoodById(entry.moodId)
                      return (
                        <motion.div
                          key={entry.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="text-2xl">{mood?.emoji}</div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start">
                              <span className="font-medium text-gray-900">{mood?.label}</span>
                              <span className="text-sm text-gray-500">
                                {formatDate(entry.timestamp)}
                              </span>
                            </div>
                            {entry.note && (
                              <p className="text-sm text-gray-600 mt-1">{entry.note}</p>
                            )}
                          </div>
                        </motion.div>
                      )
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
