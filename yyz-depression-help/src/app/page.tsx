'use client'

import { motion } from 'framer-motion'
import { Heart, Brain, MessageCircle, Calendar, Users, BookOpen, ArrowRight, Shield, Globe, Lightbulb, FileText } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const features = [
  {
    icon: Brain,
    title: '心理评估',
    description: '基于PHQ-9量表的专业抑郁症评估工具，帮助您了解自己的心理状态',
    href: '/assessment',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: MessageCircle,
    title: 'AI陪聊',
    description: '24小时温暖陪伴，AI数字伙伴倾听您的心声，提供情感支持',
    href: '/chat',
    color: 'from-purple-500 to-pink-500'
  },
  {
    icon: Calendar,
    title: '情绪日志',
    description: '记录每日情绪变化，通过数据可视化了解自己的情绪模式',
    href: '/mood',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: BookOpen,
    title: '科普知识',
    description: '权威的抑郁症科普内容，帮助您正确认识和理解心理健康',
    href: '/education',
    color: 'from-orange-500 to-red-500'
  },
  {
    icon: FileText,
    title: '医疗数据',
    description: '管理健康记录，生成专业医疗报告，便于与医生沟通',
    href: '/medical',
    color: 'from-teal-500 to-cyan-500'
  }
]

const stats = [
  { number: '3.5亿+', label: '全球抑郁症患者' },
  { number: '24/7', label: '全天候陪伴' },
  { number: '100%', label: '匿名保护' },
  { number: '0元', label: '完全免费' }
]

const values = [
  {
    icon: Shield,
    title: '隐私保护',
    description: '您的所有数据都受到严格保护，支持匿名使用'
  },
  {
    icon: Globe,
    title: '开放合作',
    description: '与医疗机构、高校、公益组织共同构建支持网络'
  },
  {
    icon: Lightbulb,
    title: '科学专业',
    description: '基于循证医学和心理学研究的专业工具和内容'
  }
]

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="relative mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mx-auto max-w-4xl text-center"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8 flex justify-center"
            >
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                <Heart className="w-10 h-10 text-white" />
              </div>
            </motion.div>

            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              <span className="block">愿所有被困在夜里的灵魂</span>
              <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                都能看到清晨的第一缕光
              </span>
            </h1>

            <p className="mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto">
              YYZ是一个以&quot;抑郁症研究与帮助&quot;为核心的公益平台。我们用科技的温度，为每一个需要帮助的人点亮希望之光。
            </p>

            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="warm" asChild>
                <Link href="/assessment" className="flex items-center space-x-2">
                  <span>开始评估</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/chat">AI陪聊</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-3xl font-bold text-blue-600 mb-2">{stat.number}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mx-auto max-w-2xl text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              我们的核心功能
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              为您提供全方位的心理健康支持服务
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardHeader className="text-center">
                      <div className={`w-12 h-12 mx-auto mb-4 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <CardTitle className="text-xl">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-center text-gray-600">
                        {feature.description}
                      </CardDescription>
                      <div className="mt-4 text-center">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={feature.href}>了解更多</Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mx-auto max-w-2xl text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              我们的价值理念
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              以人为本，科技赋能，构建温暖的数字心理健康生态
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {values.map((value, index) => {
              const Icon = value.icon
              return (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mx-auto max-w-2xl text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              加入我们，一起点亮希望
            </h2>
            <p className="mt-6 text-lg leading-8 text-blue-100">
              如果你也曾在某个夜晚想要被理解，来吧，一起为世界点亮一盏灯。
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/join" className="flex items-center space-x-2">
                  <Users className="w-5 h-5" />
                  <span>加入我们</span>
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600" asChild>
                <Link href="/education">了解更多</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold">YYZ</span>
            </div>
            <p className="text-gray-400 mb-4">
              抑郁症研究与帮助 - 愿所有被困在夜里的灵魂，都能看到清晨的第一缕光
            </p>
            <p className="text-sm text-gray-500">
              © 2024 YYZ Project. 本平台仅供参考，不能替代专业医疗建议。
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
