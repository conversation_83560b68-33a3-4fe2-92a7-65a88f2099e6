import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// PHQ-9 抑郁症评估问题
export const PHQ9_QUESTIONS = [
  {
    id: 1,
    question: "做事时提不起劲或没有兴趣",
    description: "在过去两周内，您有多少天感到做事时提不起劲或没有兴趣？"
  },
  {
    id: 2,
    question: "感到心情低落、沮丧或绝望",
    description: "在过去两周内，您有多少天感到心情低落、沮丧或绝望？"
  },
  {
    id: 3,
    question: "入睡困难、睡不安稳或睡眠过多",
    description: "在过去两周内，您有多少天有入睡困难、睡不安稳或睡眠过多的问题？"
  },
  {
    id: 4,
    question: "感觉疲倦或没有活力",
    description: "在过去两周内，您有多少天感觉疲倦或没有活力？"
  },
  {
    id: 5,
    question: "食欲不振或吃太多",
    description: "在过去两周内，您有多少天有食欲不振或吃太多的问题？"
  },
  {
    id: 6,
    question: "觉得自己很糟糕或觉得自己很失败",
    description: "在过去两周内，您有多少天觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望？"
  },
  {
    id: 7,
    question: "对事物专注有困难",
    description: "在过去两周内，您有多少天对事物专注有困难，例如阅读报纸或看电视时？"
  },
  {
    id: 8,
    question: "动作或说话速度缓慢或烦躁不安",
    description: "在过去两周内，您有多少天动作或说话速度缓慢到别人已经察觉？或正好相反——烦躁不安或坐立不安，动来动去？"
  },
  {
    id: 9,
    question: "有不如死掉或伤害自己的念头",
    description: "在过去两周内，您有多少天有不如死掉或用某种方式伤害自己的念头？"
  }
]

export const PHQ9_OPTIONS = [
  { value: 0, label: "完全没有", days: "0天" },
  { value: 1, label: "几天", days: "1-6天" },
  { value: 2, label: "一半以上的天数", days: "7-11天" },
  { value: 3, label: "几乎每天", days: "12-14天" }
]

// 评估结果解释
export function interpretPHQ9Score(score: number) {
  if (score <= 4) {
    return {
      level: "minimal",
      title: "轻微或无抑郁症状",
      description: "您目前的症状较轻微，建议继续保持良好的生活习惯。",
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    }
  } else if (score <= 9) {
    return {
      level: "mild",
      title: "轻度抑郁症状",
      description: "您可能有轻度的抑郁症状，建议关注自己的情绪变化，必要时寻求专业帮助。",
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200"
    }
  } else if (score <= 14) {
    return {
      level: "moderate",
      title: "中度抑郁症状",
      description: "您可能有中度的抑郁症状，建议尽快咨询心理健康专业人士。",
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200"
    }
  } else if (score <= 19) {
    return {
      level: "moderately-severe",
      title: "中重度抑郁症状",
      description: "您的症状较为严重，强烈建议寻求专业的心理健康服务。",
      color: "text-red-600",
      bgColor: "bg-red-50",
      borderColor: "border-red-200"
    }
  } else {
    return {
      level: "severe",
      title: "重度抑郁症状",
      description: "您的症状很严重，请立即寻求专业医疗帮助。如有自伤想法，请联系紧急服务。",
      color: "text-red-800",
      bgColor: "bg-red-100",
      borderColor: "border-red-300"
    }
  }
}

// 情绪类型
export const MOOD_TYPES = [
  { id: 'very-happy', label: '非常开心', emoji: '😄', color: '#10b981' },
  { id: 'happy', label: '开心', emoji: '😊', color: '#34d399' },
  { id: 'neutral', label: '平静', emoji: '😐', color: '#6b7280' },
  { id: 'sad', label: '难过', emoji: '😢', color: '#f59e0b' },
  { id: 'very-sad', label: '非常难过', emoji: '😭', color: '#ef4444' },
  { id: 'anxious', label: '焦虑', emoji: '😰', color: '#8b5cf6' },
  { id: 'angry', label: '愤怒', emoji: '😠', color: '#dc2626' },
  { id: 'tired', label: '疲惫', emoji: '😴', color: '#64748b' }
]

// 格式化日期
export function formatDate(date: Date): string {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 生成随机ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// 本地存储工具
export const storage = {
  get: (key: string) => {
    if (typeof window === 'undefined') return null
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch {
      return null
    }
  },
  set: (key: string, value: any) => {
    if (typeof window === 'undefined') return
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch {
      // Handle storage errors silently
    }
  },
  remove: (key: string) => {
    if (typeof window === 'undefined') return
    try {
      localStorage.removeItem(key)
    } catch {
      // Handle storage errors silently
    }
  }
}
